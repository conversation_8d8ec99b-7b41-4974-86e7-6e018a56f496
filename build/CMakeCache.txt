# This is the CMakeCache file.
# For build in directory: /Users/<USER>/work/pixman2/build
# It was generated by CMake: /usr/local/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=CMAKE_ADDR2LINE-NOTFOUND

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//No help, variable specified on the command line.
CMAKE_BUILD_TYPE:STRING=Debug

//No help, variable specified on the command line.
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/clang++

//No help, variable specified on the command line.
CMAKE_C_COMPILER:FILEPATH=/usr/bin/clang

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of build database during the build.
CMAKE_EXPORT_BUILD_DATABASE:BOOL=

//No help, variable specified on the command line.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=TRUE

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/Users/<USER>/work/pixman2/build/CMakeFiles/pkgRedirects

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//Path to a program.
CMAKE_INSTALL_NAME_TOOL:FILEPATH=/usr/bin/install_name_tool

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Program used to build from build.ninja files.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/local/bin/ninja

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=CMAKE_OBJCOPY-NOTFOUND

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Build architectures for OSX
CMAKE_OSX_ARCHITECTURES:STRING=

//Minimum OS X version to target for deployment (at runtime); newer
// APIs weak linked. Set to empty string for default value.
CMAKE_OSX_DEPLOYMENT_TARGET:STRING=

//The product will be built against the headers and libraries located
// inside the indicated SDK.
CMAKE_OSX_SYSROOT:STRING=

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=The pixman library (version 1)

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=pixman

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=0.46.5

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=46

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=5

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=CMAKE_READELF-NOTFOUND

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//Path to a program.
CMAKE_TAPI:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/tapi

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Path to a library.
MATH_LIBRARY:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd

//C compiler flags for OpenMP parallelization
OpenMP_C_FLAGS:STRING=NOTFOUND

//C compiler libraries for OpenMP parallelization
OpenMP_C_LIB_NAMES:STRING=NOTFOUND

//Path to a library.
OpenMP_libomp_LIBRARY:FILEPATH=OpenMP_libomp_LIBRARY-NOTFOUND

//Build demos
PIXMAN_BUILD_DEMOS:BOOL=ON

//Build tests
PIXMAN_BUILD_TESTS:BOOL=ON

//Path to platform-specific cpu-features.[ch] for systems that
// do not provide it (e.g. Android)
PIXMAN_CPU_FEATURES_PATH:STRING=

//Use ARM A64 NEON intrinsic optimized paths
PIXMAN_ENABLE_A64_NEON:BOOL=OFF

//Use ARMv6 SIMD intrinsic optimized paths
PIXMAN_ENABLE_ARM_SIMD:BOOL=OFF

//Enable output of filters that can be piped to gnuplot
PIXMAN_ENABLE_GNUPLOT:BOOL=OFF

//Use GNU style inline assembler
PIXMAN_ENABLE_GNU_INLINE_ASM:BOOL=OFF

//Enable demos using GTK
PIXMAN_ENABLE_GTK:BOOL=ON

//Use libpng in tests
PIXMAN_ENABLE_LIBPNG:BOOL=ON

//Use Loongson MMI intrinsic optimized paths
PIXMAN_ENABLE_LOONGSON_MMI:BOOL=OFF

//Use MIPS32 DSPr2 intrinsic optimized paths
PIXMAN_ENABLE_MIPS_DSPR2:BOOL=OFF

//Use X86 MMX intrinsic optimized paths
PIXMAN_ENABLE_MMX:BOOL=OFF

//Use ARM NEON intrinsic optimized paths
PIXMAN_ENABLE_NEON:BOOL=OFF

//Enable OpenMP for tests
PIXMAN_ENABLE_OPENMP:BOOL=ON

//Use RISC-V Vector extension
PIXMAN_ENABLE_RVV:BOOL=OFF

//Use X86 SSE2 intrinsic optimized paths
PIXMAN_ENABLE_SSE2:BOOL=OFF

//Use X86 SSSE3 intrinsic optimized paths
PIXMAN_ENABLE_SSSE3:BOOL=OFF

//Enable TIMER_* macros
PIXMAN_ENABLE_TIMERS:BOOL=OFF

//Use compiler support for thread-local storage
PIXMAN_ENABLE_TLS:BOOL=OFF

//Use PPC VMX/Altivec intrinsic optimized paths
PIXMAN_ENABLE_VMX:BOOL=OFF

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/usr/local/bin/pkg-config

//Path to a library.
PNG_LIBRARY_DEBUG:FILEPATH=PNG_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
PNG_LIBRARY_RELEASE:FILEPATH=/usr/local/lib/libpng.dylib

//Path to a file.
PNG_PNG_INCLUDE_DIR:PATH=/usr/local/include

//Path to a file.
ZLIB_INCLUDE_DIR:PATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include

//Path to a library.
ZLIB_LIBRARY_DEBUG:FILEPATH=ZLIB_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
ZLIB_LIBRARY_RELEASE:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd

//Value Computed by CMake
pixman_BINARY_DIR:STATIC=/Users/<USER>/work/pixman2/build

//Value Computed by CMake
pixman_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
pixman_SOURCE_DIR:STATIC=/Users/<USER>/work/pixman2

//Path to a library.
pkgcfg_lib_GLIB2_glib-2.0:FILEPATH=/usr/local/Cellar/glib/2.84.2/lib/libglib-2.0.dylib

//Path to a library.
pkgcfg_lib_GLIB2_intl:FILEPATH=/usr/local/opt/gettext/lib/libintl.dylib

//Path to a library.
pkgcfg_lib_GTK3_atk-1.0:FILEPATH=/usr/local/lib/libatk-1.0.dylib

//Path to a library.
pkgcfg_lib_GTK3_cairo:FILEPATH=/usr/local/lib/libcairo.a

//Path to a library.
pkgcfg_lib_GTK3_cairo-gobject:FILEPATH=/usr/local/lib/libcairo-gobject.dylib

//Path to a library.
pkgcfg_lib_GTK3_dl:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libdl.tbd

//Path to a library.
pkgcfg_lib_GTK3_fontconfig:FILEPATH=/usr/local/lib/libfontconfig.dylib

//Path to a library.
pkgcfg_lib_GTK3_freetype:FILEPATH=/usr/local/lib/libfreetype.dylib

//Path to a library.
pkgcfg_lib_GTK3_gdk-3:FILEPATH=/usr/local/Cellar/gtk+3/3.24.43/lib/libgdk-3.dylib

//Path to a library.
pkgcfg_lib_GTK3_gdk_pixbuf-2.0:FILEPATH=/usr/local/lib/libgdk_pixbuf-2.0.dylib

//Path to a library.
pkgcfg_lib_GTK3_gio-2.0:FILEPATH=/usr/local/lib/libgio-2.0.dylib

//Path to a library.
pkgcfg_lib_GTK3_glib-2.0:FILEPATH=/usr/local/lib/libglib-2.0.dylib

//Path to a library.
pkgcfg_lib_GTK3_gobject-2.0:FILEPATH=/usr/local/lib/libgobject-2.0.dylib

//Path to a library.
pkgcfg_lib_GTK3_gtk-3:FILEPATH=/usr/local/Cellar/gtk+3/3.24.43/lib/libgtk-3.dylib

//Path to a library.
pkgcfg_lib_GTK3_harfbuzz:FILEPATH=/usr/local/Cellar/harfbuzz/11.1.0/lib/libharfbuzz.dylib

//Path to a library.
pkgcfg_lib_GTK3_intl:FILEPATH=/usr/local/lib/libintl.dylib

//Path to a library.
pkgcfg_lib_GTK3_m:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd

//Path to a library.
pkgcfg_lib_GTK3_pango-1.0:FILEPATH=/usr/local/Cellar/pango/1.56.3/lib/libpango-1.0.dylib

//Path to a library.
pkgcfg_lib_GTK3_pangocairo-1.0:FILEPATH=/usr/local/Cellar/pango/1.56.3/lib/libpangocairo-1.0.dylib

//Path to a library.
pkgcfg_lib_GTK3_pixman-1:FILEPATH=/usr/local/lib/libpixman-1.dylib

//Path to a library.
pkgcfg_lib_GTK3_png16:FILEPATH=/usr/local/lib/libpng16.dylib

//Path to a library.
pkgcfg_lib_GTK3_z:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd


########################
# INTERNAL cache entries
########################

//Test ASM_HAVE_FUNC_DIRECTIVE
ASM_HAVE_FUNC_DIRECTIVE:INTERNAL=
//Test ASM_HAVE_SYNTAX_UNIFIED
ASM_HAVE_SYNTAX_UNIFIED:INTERNAL=
//Test ASM_LEADING_UNDERSCORE
ASM_LEADING_UNDERSCORE:INTERNAL=
//Result of TRY_COMPILE
ASM_LEADING_UNDERSCORE_COMPILED:INTERNAL=FALSE
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/Users/<USER>/work/pixman2/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=4
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=0
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=2
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/local/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/local/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/local/bin/ctest
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/usr/local/bin/ccmake
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=MACHO
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_BUILD_DATABASE
CMAKE_EXPORT_BUILD_DATABASE-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Ninja
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/Users/<USER>/work/pixman2
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_NAME_TOOL
CMAKE_INSTALL_NAME_TOOL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//Name of CMakeLists files to read
CMAKE_LIST_FILE_NAME:INTERNAL=CMakeLists.txt
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=5
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/local/share/cmake
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding PNG
FIND_PACKAGE_MESSAGE_DETAILS_PNG:INTERNAL=[/usr/local/lib/libpng.dylib][/usr/local/include][v1.6.48()]
//Details about finding PkgConfig
FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig:INTERNAL=[/usr/local/bin/pkg-config][v2.4.3()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//Details about finding ZLIB
FIND_PACKAGE_MESSAGE_DETAILS_ZLIB:INTERNAL=[/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd][/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include][ ][v1.2.11()]
GLIB2_CFLAGS:INTERNAL=-I/usr/local/Cellar/glib/2.84.2/include/glib-2.0;-I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include;-I/usr/local/opt/gettext/include;-I/usr/local/Cellar/pcre2/10.45/include
GLIB2_CFLAGS_I:INTERNAL=
GLIB2_CFLAGS_OTHER:INTERNAL=
GLIB2_FOUND:INTERNAL=1
GLIB2_INCLUDEDIR:INTERNAL=/usr/local/Cellar/glib/2.84.2/include
GLIB2_INCLUDE_DIRS:INTERNAL=/usr/local/Cellar/glib/2.84.2/include/glib-2.0;/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include;/usr/local/opt/gettext/include;/usr/local/Cellar/pcre2/10.45/include
GLIB2_LDFLAGS:INTERNAL=-L/usr/local/Cellar/glib/2.84.2/lib;-lglib-2.0;-L/usr/local/opt/gettext/lib;-lintl
GLIB2_LDFLAGS_OTHER:INTERNAL=
GLIB2_LIBDIR:INTERNAL=/usr/local/Cellar/glib/2.84.2/lib
GLIB2_LIBRARIES:INTERNAL=glib-2.0;intl
GLIB2_LIBRARY_DIRS:INTERNAL=/usr/local/Cellar/glib/2.84.2/lib;/usr/local/opt/gettext/lib
GLIB2_LIBS:INTERNAL=
GLIB2_LIBS_L:INTERNAL=
GLIB2_LIBS_OTHER:INTERNAL=
GLIB2_LIBS_PATHS:INTERNAL=
GLIB2_MODULE_NAME:INTERNAL=glib-2.0
GLIB2_PREFIX:INTERNAL=/usr/local/Cellar/glib/2.84.2
GLIB2_STATIC_CFLAGS:INTERNAL=-I/usr/local/Cellar/glib/2.84.2/include/glib-2.0;-I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include;-I/usr/local/opt/gettext/include;-I/usr/local/Cellar/pcre2/10.45/include
GLIB2_STATIC_CFLAGS_I:INTERNAL=
GLIB2_STATIC_CFLAGS_OTHER:INTERNAL=
GLIB2_STATIC_INCLUDE_DIRS:INTERNAL=/usr/local/Cellar/glib/2.84.2/include/glib-2.0;/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include;/usr/local/opt/gettext/include;/usr/local/Cellar/pcre2/10.45/include
GLIB2_STATIC_LDFLAGS:INTERNAL=-L/usr/local/Cellar/glib/2.84.2/lib;-lglib-2.0;-L/usr/local/opt/gettext/lib;-lintl;-liconv;-lm;-framework;Foundation;-framework;CoreFoundation;-framework;AppKit;-framework;Carbon;-L/usr/local/Cellar/pcre2/10.45/lib;-lpcre2-8;-D_THREAD_SAFE;-pthread
GLIB2_STATIC_LDFLAGS_OTHER:INTERNAL=-framework;Foundation;-framework;CoreFoundation;-framework;AppKit;-framework;Carbon;-D_THREAD_SAFE;-pthread
GLIB2_STATIC_LIBDIR:INTERNAL=
GLIB2_STATIC_LIBRARIES:INTERNAL=glib-2.0;intl;iconv;m;pcre2-8
GLIB2_STATIC_LIBRARY_DIRS:INTERNAL=/usr/local/Cellar/glib/2.84.2/lib;/usr/local/opt/gettext/lib;/usr/local/Cellar/pcre2/10.45/lib
GLIB2_STATIC_LIBS:INTERNAL=
GLIB2_STATIC_LIBS_L:INTERNAL=
GLIB2_STATIC_LIBS_OTHER:INTERNAL=
GLIB2_STATIC_LIBS_PATHS:INTERNAL=
GLIB2_VERSION:INTERNAL=2.84.2
GLIB2_glib-2.0_INCLUDEDIR:INTERNAL=
GLIB2_glib-2.0_LIBDIR:INTERNAL=
GLIB2_glib-2.0_PREFIX:INTERNAL=
GLIB2_glib-2.0_VERSION:INTERNAL=
GTK3_CFLAGS:INTERNAL=-I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0;-I/usr/local/Cellar/pango/1.56.3/include/pango-1.0;-I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz;-I/usr/local/Cellar/graphite2/1.3.14/include;-I/usr/local/include;-I/usr/local/include/cairo;-I/usr/local/opt/freetype/include/freetype2;-I/usr/local/Cellar/pixman/0.46.0/include/pixman-1;-I/usr/local/include/gdk-pixbuf-2.0;-I/usr/local/opt/libpng/include/libpng16;-I/usr/local/opt/libtiff/include;-I/usr/local/opt/zstd/include;-I/usr/local/Cellar/xz/5.8.1/include;-I/usr/local/opt/jpeg-turbo/include;-I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0;-I/usr/local/Cellar/libepoxy/1.5.10/include;-I/usr/local/Cellar/fribidi/1.0.16/include/fribidi;-I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0;-I/usr/local/Cellar/glib/2.84.2/include;-I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi;-I/usr/local/Cellar/glib/2.84.2/include/glib-2.0;-I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include;-I/usr/local/opt/gettext/include;-I/usr/local/Cellar/pcre2/10.45/include
GTK3_CFLAGS_I:INTERNAL=
GTK3_CFLAGS_OTHER:INTERNAL=
GTK3_FOUND:INTERNAL=1
GTK3_INCLUDEDIR:INTERNAL=/usr/local/Cellar/gtk+3/3.24.43/include
GTK3_INCLUDE_DIRS:INTERNAL=/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0;/usr/local/Cellar/pango/1.56.3/include/pango-1.0;/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz;/usr/local/Cellar/graphite2/1.3.14/include;/usr/local/include;/usr/local/include/cairo;/usr/local/opt/freetype/include/freetype2;/usr/local/Cellar/pixman/0.46.0/include/pixman-1;/usr/local/include/gdk-pixbuf-2.0;/usr/local/opt/libpng/include/libpng16;/usr/local/opt/libtiff/include;/usr/local/opt/zstd/include;/usr/local/Cellar/xz/5.8.1/include;/usr/local/opt/jpeg-turbo/include;/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0;/usr/local/Cellar/libepoxy/1.5.10/include;/usr/local/Cellar/fribidi/1.0.16/include/fribidi;/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0;/usr/local/Cellar/glib/2.84.2/include;/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi;/usr/local/Cellar/glib/2.84.2/include/glib-2.0;/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include;/usr/local/opt/gettext/include;/usr/local/Cellar/pcre2/10.45/include
GTK3_LDFLAGS:INTERNAL=-L/usr/local/Cellar/gtk+3/3.24.43/lib;-lgtk-3;-lgdk-3;-Wl,-framework,Cocoa;-Wl,-framework,Carbon;-Wl,-framework,CoreGraphics;-L/usr/local/Cellar/pango/1.56.3/lib;-lpangocairo-1.0;-lpango-1.0;-L/usr/local/Cellar/harfbuzz/11.1.0/lib;-lharfbuzz;-L/usr/local/lib;-lcairo-gobject;-lcairo;-lm;-ldl;-framework;CoreFoundation;-framework;ApplicationServices;-lfontconfig;-L/usr/local/opt/freetype/lib;-lfreetype;-L/usr/local/Cellar/pixman/0.46.0/lib;-lpixman-1;-lgdk_pixbuf-2.0;-L/usr/local/opt/libpng/lib;-lpng16;-L/usr/local/Cellar/at-spi2-core/2.56.1/lib;-latk-1.0;-L/usr/local/Cellar/glib/2.84.2/lib;-lgio-2.0;-lgobject-2.0;-lglib-2.0;-L/usr/local/opt/gettext/lib;-lintl;-L/usr/lib;-lz
GTK3_LDFLAGS_OTHER:INTERNAL=-Wl,-framework,Cocoa;-Wl,-framework,Carbon;-Wl,-framework,CoreGraphics
GTK3_LIBDIR:INTERNAL=/usr/local/Cellar/gtk+3/3.24.43/lib
GTK3_LIBRARIES:INTERNAL=gtk-3;gdk-3;pangocairo-1.0;pango-1.0;harfbuzz;cairo-gobject;cairo;m;dl;fontconfig;freetype;pixman-1;gdk_pixbuf-2.0;png16;atk-1.0;gio-2.0;gobject-2.0;glib-2.0;intl;z;-framework CoreFoundation;-framework ApplicationServices
GTK3_LIBRARY_DIRS:INTERNAL=/usr/local/Cellar/gtk+3/3.24.43/lib;/usr/local/Cellar/pango/1.56.3/lib;/usr/local/Cellar/harfbuzz/11.1.0/lib;/usr/local/lib;/usr/local/opt/freetype/lib;/usr/local/Cellar/pixman/0.46.0/lib;/usr/local/opt/libpng/lib;/usr/local/Cellar/at-spi2-core/2.56.1/lib;/usr/local/Cellar/glib/2.84.2/lib;/usr/local/opt/gettext/lib;/usr/lib
GTK3_LIBS:INTERNAL=
GTK3_LIBS_L:INTERNAL=
GTK3_LIBS_OTHER:INTERNAL=
GTK3_LIBS_PATHS:INTERNAL=
GTK3_MODULE_NAME:INTERNAL=gtk+-3.0
GTK3_PREFIX:INTERNAL=/usr/local/Cellar/gtk+3/3.24.43
GTK3_STATIC_CFLAGS:INTERNAL=-I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0;-I/usr/local/Cellar/pango/1.56.3/include/pango-1.0;-I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz;-I/usr/local/Cellar/graphite2/1.3.14/include;-I/usr/local/include;-I/usr/local/include/cairo;-I/usr/local/opt/freetype/include/freetype2;-I/usr/local/Cellar/pixman/0.46.0/include/pixman-1;-I/usr/local/include/gdk-pixbuf-2.0;-I/usr/local/opt/libpng/include/libpng16;-I/usr/local/opt/libtiff/include;-I/usr/local/opt/zstd/include;-I/usr/local/Cellar/xz/5.8.1/include;-I/usr/local/opt/jpeg-turbo/include;-I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0;-I/usr/local/Cellar/libepoxy/1.5.10/include;-I/usr/local/Cellar/fribidi/1.0.16/include/fribidi;-I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0;-I/usr/local/Cellar/glib/2.84.2/include;-I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi;-I/usr/local/Cellar/glib/2.84.2/include/glib-2.0;-I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include;-I/usr/local/opt/gettext/include;-I/usr/local/Cellar/pcre2/10.45/include;-DLZMA_API_STATIC;-DFRIBIDI_LIB_STATIC
GTK3_STATIC_CFLAGS_I:INTERNAL=
GTK3_STATIC_CFLAGS_OTHER:INTERNAL=-DLZMA_API_STATIC;-DFRIBIDI_LIB_STATIC
GTK3_STATIC_INCLUDE_DIRS:INTERNAL=/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0;/usr/local/Cellar/pango/1.56.3/include/pango-1.0;/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz;/usr/local/Cellar/graphite2/1.3.14/include;/usr/local/include;/usr/local/include/cairo;/usr/local/opt/freetype/include/freetype2;/usr/local/Cellar/pixman/0.46.0/include/pixman-1;/usr/local/include/gdk-pixbuf-2.0;/usr/local/opt/libpng/include/libpng16;/usr/local/opt/libtiff/include;/usr/local/opt/zstd/include;/usr/local/Cellar/xz/5.8.1/include;/usr/local/opt/jpeg-turbo/include;/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0;/usr/local/Cellar/libepoxy/1.5.10/include;/usr/local/Cellar/fribidi/1.0.16/include/fribidi;/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0;/usr/local/Cellar/glib/2.84.2/include;/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi;/usr/local/Cellar/glib/2.84.2/include/glib-2.0;/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include;/usr/local/opt/gettext/include;/usr/local/Cellar/pcre2/10.45/include
GTK3_STATIC_LDFLAGS:INTERNAL=-L/usr/local/Cellar/gtk+3/3.24.43/lib;-lgtk-3;-lgdk-3;-Wl,-framework,Cocoa;-Wl,-framework,Carbon;-Wl,-framework,CoreGraphics;-L/usr/local/Cellar/pango/1.56.3/lib;-lpangocairo-1.0;-lm;-framework;CoreFoundation;-framework;ApplicationServices;-lpangoft2-1.0;-lm;-framework;CoreFoundation;-framework;ApplicationServices;-lpango-1.0;-lm;-framework;CoreFoundation;-framework;ApplicationServices;-L/usr/local/Cellar/harfbuzz/11.1.0/lib;-lharfbuzz-gobject;-lharfbuzz;-framework;ApplicationServices;-L/usr/local/Cellar/graphite2/1.3.14/lib;-lgraphite2;-framework;CoreFoundation;-framework;ApplicationServices;-L/usr/local/lib;-lcairo-gobject;-lcairo;-lm;-ldl;-framework;CoreFoundation;-framework;ApplicationServices;-lfontconfig;-lintl;-lm;-L/usr/lib;-lexpat;-L/usr/local/opt/freetype/lib;-lfreetype;-lbz2;-L/usr/local/Cellar/pixman/0.46.0/lib;-lpixman-1;-lm;-lgdk_pixbuf-2.0;-lm;-lintl;-L/usr/local/opt/libpng/lib;-lpng16;-lz;-L/usr/local/opt/libtiff/lib;-ltiff;-L/usr/local/opt/zstd/lib;-lzstd;-L/usr/local/Cellar/xz/5.8.1/lib;-llzma;-pthread;-lpthread;-L/usr/local/opt/jpeg-turbo/lib;-ljpeg;-L/usr/local/Cellar/at-spi2-core/2.56.1/lib;-latk-1.0;-L/usr/local/Cellar/libepoxy/1.5.10/lib;-lepoxy;-ldl;-L/usr/local/Cellar/fribidi/1.0.16/lib;-lfribidi;-L/usr/local/Cellar/glib/2.84.2/lib;-lgio-2.0;-lintl;-framework;Foundation;-framework;CoreFoundation;-framework;AppKit;-lresolv;-lgobject-2.0;-lintl;-lffi;-lgmodule-2.0;-lglib-2.0;-L/usr/local/opt/gettext/lib;-lintl;-liconv;-lm;-framework;Foundation;-framework;CoreFoundation;-framework;AppKit;-framework;Carbon;-L/usr/local/Cellar/pcre2/10.45/lib;-lpcre2-8;-D_THREAD_SAFE;-pthread;-lz
GTK3_STATIC_LDFLAGS_OTHER:INTERNAL=-Wl,-framework,Cocoa;-Wl,-framework,Carbon;-Wl,-framework,CoreGraphics;-framework;CoreFoundation;-framework;ApplicationServices;-framework;CoreFoundation;-framework;ApplicationServices;-framework;CoreFoundation;-framework;ApplicationServices;-framework;ApplicationServices;-framework;CoreFoundation;-framework;ApplicationServices;-framework;CoreFoundation;-framework;ApplicationServices;-pthread;-framework;Foundation;-framework;CoreFoundation;-framework;AppKit;-lresolv;-framework;Foundation;-framework;CoreFoundation;-framework;AppKit;-framework;Carbon;-D_THREAD_SAFE;-pthread
GTK3_STATIC_LIBDIR:INTERNAL=
GTK3_STATIC_LIBRARIES:INTERNAL=gtk-3;gdk-3;pangocairo-1.0;m;pangoft2-1.0;m;pango-1.0;m;harfbuzz-gobject;harfbuzz;graphite2;cairo-gobject;cairo;m;dl;fontconfig;intl;m;expat;freetype;bz2;pixman-1;m;gdk_pixbuf-2.0;m;intl;png16;z;tiff;zstd;lzma;pthread;jpeg;atk-1.0;epoxy;dl;fribidi;gio-2.0;intl;gobject-2.0;intl;ffi;gmodule-2.0;glib-2.0;intl;iconv;m;pcre2-8;z
GTK3_STATIC_LIBRARY_DIRS:INTERNAL=/usr/local/Cellar/gtk+3/3.24.43/lib;/usr/local/Cellar/pango/1.56.3/lib;/usr/local/Cellar/harfbuzz/11.1.0/lib;/usr/local/Cellar/graphite2/1.3.14/lib;/usr/local/lib;/usr/lib;/usr/local/opt/freetype/lib;/usr/local/Cellar/pixman/0.46.0/lib;/usr/local/opt/libpng/lib;/usr/local/opt/libtiff/lib;/usr/local/opt/zstd/lib;/usr/local/Cellar/xz/5.8.1/lib;/usr/local/opt/jpeg-turbo/lib;/usr/local/Cellar/at-spi2-core/2.56.1/lib;/usr/local/Cellar/libepoxy/1.5.10/lib;/usr/local/Cellar/fribidi/1.0.16/lib;/usr/local/Cellar/glib/2.84.2/lib;/usr/local/opt/gettext/lib;/usr/local/Cellar/pcre2/10.45/lib
GTK3_STATIC_LIBS:INTERNAL=
GTK3_STATIC_LIBS_L:INTERNAL=
GTK3_STATIC_LIBS_OTHER:INTERNAL=
GTK3_STATIC_LIBS_PATHS:INTERNAL=
GTK3_VERSION:INTERNAL=3.24.43
GTK3_gtk+-3.0_INCLUDEDIR:INTERNAL=
GTK3_gtk+-3.0_LIBDIR:INTERNAL=
GTK3_gtk+-3.0_PREFIX:INTERNAL=
GTK3_gtk+-3.0_VERSION:INTERNAL=
//Have function alarm
HAVE_ALARM:INTERNAL=1
//Test HAVE_BUILTIN_CLZ
HAVE_BUILTIN_CLZ:INTERNAL=1
//Have symbol FE_DIVBYZERO
HAVE_FEDIVBYZERO:INTERNAL=1
//Have symbol feenableexcept
HAVE_FEENABLEEXCEPT:INTERNAL=
//Have include fenv.h
HAVE_FENV_H:INTERNAL=1
//Test HAVE_FLOAT128
HAVE_FLOAT128:INTERNAL=
//Test HAVE_GCC_VECTOR_EXTENSIONS
HAVE_GCC_VECTOR_EXTENSIONS:INTERNAL=1
//Have function getisax
HAVE_GETISAX:INTERNAL=
//Have function getpagesize
HAVE_GETPAGESIZE:INTERNAL=1
//Have function gettimeofday
HAVE_GETTIMEOFDAY:INTERNAL=1
//Have function mmap
HAVE_MMAP:INTERNAL=1
//Have function mprotect
HAVE_MPROTECT:INTERNAL=1
//Have function posix_memalign
HAVE_POSIX_MEMALIGN:INTERNAL=1
//Have include pthread.h
HAVE_PTHREAD_H:INTERNAL=1
//Have function sigaction
HAVE_SIGACTION:INTERNAL=1
//Result of TRY_COMPILE
HAVE_SIZEOF_LONG:INTERNAL=TRUE
//Have include stddef.h
HAVE_STDDEF_H:INTERNAL=1
//Have include stdint.h
HAVE_STDINT_H:INTERNAL=1
//Have include sys/mman.h
HAVE_SYS_MMAN_H:INTERNAL=1
//Have include sys/types.h
HAVE_SYS_TYPES_H:INTERNAL=1
//Have include unistd.h
HAVE_UNISTD_H:INTERNAL=1
//Result of TRY_COMPILE
OpenMP_COMPILE_RESULT_C_Xclang fopenmp:INTERNAL=FALSE
//ADVANCED property for variable: OpenMP_C_FLAGS
OpenMP_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_C_LIB_NAMES
OpenMP_C_LIB_NAMES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_libomp_LIBRARY
OpenMP_libomp_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PNG_LIBRARY_DEBUG
PNG_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PNG_LIBRARY_RELEASE
PNG_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PNG_PNG_INCLUDE_DIR
PNG_PNG_INCLUDE_DIR-ADVANCED:INTERNAL=1
//CHECK_TYPE_SIZE: sizeof(long)
SIZEOF_LONG:INTERNAL=8
//Test TOOLCHAIN_SUPPORTS_ATTRIBUTE_CONSTRUCTOR
TOOLCHAIN_SUPPORTS_ATTRIBUTE_CONSTRUCTOR:INTERNAL=1
//Result of TRY_COMPILE
TOOLCHAIN_SUPPORTS_ATTRIBUTE_CONSTRUCTOR_COMPILED:INTERNAL=TRUE
//Result of try_run()
TOOLCHAIN_SUPPORTS_ATTRIBUTE_CONSTRUCTOR_EXITCODE:INTERNAL=0
//Test TOOLCHAIN_SUPPORTS_ATTRIBUTE_DESTRUCTOR
TOOLCHAIN_SUPPORTS_ATTRIBUTE_DESTRUCTOR:INTERNAL=
//Result of TRY_COMPILE
TOOLCHAIN_SUPPORTS_ATTRIBUTE_DESTRUCTOR_COMPILED:INTERNAL=TRUE
//Result of try_run()
TOOLCHAIN_SUPPORTS_ATTRIBUTE_DESTRUCTOR_EXITCODE:INTERNAL=1
//ADVANCED property for variable: ZLIB_INCLUDE_DIR
ZLIB_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_DEBUG
ZLIB_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ZLIB_LIBRARY_RELEASE
ZLIB_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=/usr/local
__pkg_config_arguments_GLIB2:INTERNAL=glib-2.0
__pkg_config_arguments_GTK3:INTERNAL=gtk+-3.0
__pkg_config_checked_GLIB2:INTERNAL=1
__pkg_config_checked_GTK3:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GLIB2_glib-2.0
pkgcfg_lib_GLIB2_glib-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GLIB2_intl
pkgcfg_lib_GLIB2_intl-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_atk-1.0
pkgcfg_lib_GTK3_atk-1.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_cairo
pkgcfg_lib_GTK3_cairo-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_cairo-gobject
pkgcfg_lib_GTK3_cairo-gobject-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_dl
pkgcfg_lib_GTK3_dl-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_fontconfig
pkgcfg_lib_GTK3_fontconfig-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_freetype
pkgcfg_lib_GTK3_freetype-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_gdk-3
pkgcfg_lib_GTK3_gdk-3-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_gdk_pixbuf-2.0
pkgcfg_lib_GTK3_gdk_pixbuf-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_gio-2.0
pkgcfg_lib_GTK3_gio-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_glib-2.0
pkgcfg_lib_GTK3_glib-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_gobject-2.0
pkgcfg_lib_GTK3_gobject-2.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_gtk-3
pkgcfg_lib_GTK3_gtk-3-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_harfbuzz
pkgcfg_lib_GTK3_harfbuzz-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_intl
pkgcfg_lib_GTK3_intl-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_m
pkgcfg_lib_GTK3_m-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_pango-1.0
pkgcfg_lib_GTK3_pango-1.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_pangocairo-1.0
pkgcfg_lib_GTK3_pangocairo-1.0-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_pixman-1
pkgcfg_lib_GTK3_pixman-1-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_png16
pkgcfg_lib_GTK3_png16-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_GTK3_z
pkgcfg_lib_GTK3_z-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/usr/local/Cellar/glib/2.84.2/lib

