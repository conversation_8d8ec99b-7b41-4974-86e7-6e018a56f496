Start testing: Jul 21 20:34 CST
----------------------------------------------------------
1/35 Testing: oob-test
1/35 Test: oob-test
Command: "/Users/<USER>/work/pixman2/build/test/oob-test"
Directory: /Users/<USER>/work/pixman2/build/test
"oob-test" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
<end of output>
Test time =   0.01 sec
----------------------------------------------------------
Test Passed.
"oob-test" end time: Jul 21 20:34 CST
"oob-test" time elapsed: 00:00:00
----------------------------------------------------------

2/35 Testing: infinite-loop
2/35 Test: infinite-loop
Command: "/Users/<USER>/work/pixman2/build/test/infinite-loop"
Directory: /Users/<USER>/work/pixman2/build/test
"infinite-loop" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
<end of output>
Test time =   0.01 sec
----------------------------------------------------------
Test Passed.
"infinite-loop" end time: Jul 21 20:34 CST
"infinite-loop" time elapsed: 00:00:00
----------------------------------------------------------

3/35 Testing: trap-crasher
3/35 Test: trap-crasher
Command: "/Users/<USER>/work/pixman2/build/test/trap-crasher"
Directory: /Users/<USER>/work/pixman2/build/test
"trap-crasher" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
<end of output>
Test time =   0.01 sec
----------------------------------------------------------
Test Passed.
"trap-crasher" end time: Jul 21 20:34 CST
"trap-crasher" time elapsed: 00:00:00
----------------------------------------------------------

5/35 Testing: region-translate-test
5/35 Test: region-translate-test
Command: "/Users/<USER>/work/pixman2/build/test/region-translate-test"
Directory: /Users/<USER>/work/pixman2/build/test
"region-translate-test" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
<end of output>
Test time =   0.00 sec
----------------------------------------------------------
Test Passed.
"region-translate-test" end time: Jul 21 20:34 CST
"region-translate-test" time elapsed: 00:00:00
----------------------------------------------------------

6/35 Testing: fetch-test
6/35 Test: fetch-test
Command: "/Users/<USER>/work/pixman2/build/test/fetch-test"
Directory: /Users/<USER>/work/pixman2/build/test
"fetch-test" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
<end of output>
Test time =   0.00 sec
----------------------------------------------------------
Test Passed.
"fetch-test" end time: Jul 21 20:34 CST
"fetch-test" time elapsed: 00:00:00
----------------------------------------------------------

7/35 Testing: a1-trap-test
7/35 Test: a1-trap-test
Command: "/Users/<USER>/work/pixman2/build/test/a1-trap-test"
Directory: /Users/<USER>/work/pixman2/build/test
"a1-trap-test" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
<end of output>
Test time =   0.01 sec
----------------------------------------------------------
Test Passed.
"a1-trap-test" end time: Jul 21 20:34 CST
"a1-trap-test" time elapsed: 00:00:00
----------------------------------------------------------

8/35 Testing: prng-test
8/35 Test: prng-test
Command: "/Users/<USER>/work/pixman2/build/test/prng-test"
Directory: /Users/<USER>/work/pixman2/build/test
"prng-test" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
<end of output>
Test time =   0.01 sec
----------------------------------------------------------
Test Passed.
"prng-test" end time: Jul 21 20:34 CST
"prng-test" time elapsed: 00:00:00
----------------------------------------------------------

9/35 Testing: radial-invalid
9/35 Test: radial-invalid
Command: "/Users/<USER>/work/pixman2/build/test/radial-invalid"
Directory: /Users/<USER>/work/pixman2/build/test
"radial-invalid" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
<end of output>
Test time =   0.01 sec
----------------------------------------------------------
Test Passed.
"radial-invalid" end time: Jul 21 20:34 CST
"radial-invalid" time elapsed: 00:00:00
----------------------------------------------------------

10/35 Testing: pdf-op-test
10/35 Test: pdf-op-test
Command: "/Users/<USER>/work/pixman2/build/test/pdf-op-test"
Directory: /Users/<USER>/work/pixman2/build/test
"pdf-op-test" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
<end of output>
Test time =   0.01 sec
----------------------------------------------------------
Test Passed.
"pdf-op-test" end time: Jul 21 20:34 CST
"pdf-op-test" time elapsed: 00:00:00
----------------------------------------------------------

4/35 Testing: fence-image-self-test
4/35 Test: fence-image-self-test
Command: "/Users/<USER>/work/pixman2/build/test/fence-image-self-test"
Directory: /Users/<USER>/work/pixman2/build/test
"fence-image-self-test" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
<end of output>
Test time =   0.04 sec
----------------------------------------------------------
Test Passed.
"fence-image-self-test" end time: Jul 21 20:34 CST
"fence-image-self-test" time elapsed: 00:00:00
----------------------------------------------------------

11/35 Testing: region-test
11/35 Test: region-test
Command: "/Users/<USER>/work/pixman2/build/test/region-test"
Directory: /Users/<USER>/work/pixman2/build/test
"region-test" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
<end of output>
Test time =   0.03 sec
----------------------------------------------------------
Test Passed.
"region-test" end time: Jul 21 20:34 CST
"region-test" time elapsed: 00:00:00
----------------------------------------------------------

12/35 Testing: region-fractional-test
12/35 Test: region-fractional-test
Command: "/Users/<USER>/work/pixman2/build/test/region-fractional-test"
Directory: /Users/<USER>/work/pixman2/build/test
"region-fractional-test" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
<end of output>
Test time =   0.03 sec
----------------------------------------------------------
Test Passed.
"region-fractional-test" end time: Jul 21 20:34 CST
"region-fractional-test" time elapsed: 00:00:00
----------------------------------------------------------

13/35 Testing: combiner-test
13/35 Test: combiner-test
Command: "/Users/<USER>/work/pixman2/build/test/combiner-test"
Directory: /Users/<USER>/work/pixman2/build/test
"combiner-test" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
<end of output>
Test time =   0.02 sec
----------------------------------------------------------
Test Passed.
"combiner-test" end time: Jul 21 20:34 CST
"combiner-test" time elapsed: 00:00:00
----------------------------------------------------------

14/35 Testing: scaling-crash-test
14/35 Test: scaling-crash-test
Command: "/Users/<USER>/work/pixman2/build/test/scaling-crash-test"
Directory: /Users/<USER>/work/pixman2/build/test
"scaling-crash-test" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
<end of output>
Test time =   0.03 sec
----------------------------------------------------------
Test Passed.
"scaling-crash-test" end time: Jul 21 20:34 CST
"scaling-crash-test" time elapsed: 00:00:00
----------------------------------------------------------

15/35 Testing: alpha-loop
15/35 Test: alpha-loop
Command: "/Users/<USER>/work/pixman2/build/test/alpha-loop"
Directory: /Users/<USER>/work/pixman2/build/test
"alpha-loop" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
<end of output>
Test time =   0.04 sec
----------------------------------------------------------
Test Passed.
"alpha-loop" end time: Jul 21 20:34 CST
"alpha-loop" time elapsed: 00:00:00
----------------------------------------------------------

16/35 Testing: scaling-helpers-test
16/35 Test: scaling-helpers-test
Command: "/Users/<USER>/work/pixman2/build/test/scaling-helpers-test"
Directory: /Users/<USER>/work/pixman2/build/test
"scaling-helpers-test" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
<end of output>
Test time =   0.17 sec
----------------------------------------------------------
Test Passed.
"scaling-helpers-test" end time: Jul 21 20:34 CST
"scaling-helpers-test" time elapsed: 00:00:00
----------------------------------------------------------

19/35 Testing: gradient-crash-test
19/35 Test: gradient-crash-test
Command: "/Users/<USER>/work/pixman2/build/test/gradient-crash-test"
Directory: /Users/<USER>/work/pixman2/build/test
"gradient-crash-test" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
<end of output>
Test time =   0.45 sec
----------------------------------------------------------
Test Passed.
"gradient-crash-test" end time: Jul 21 20:34 CST
"gradient-crash-test" time elapsed: 00:00:00
----------------------------------------------------------

17/35 Testing: rotate-test
17/35 Test: rotate-test
Command: "/Users/<USER>/work/pixman2/build/test/rotate-test"
Directory: /Users/<USER>/work/pixman2/build/test
"rotate-test" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
rotate test passed (checksum=81E9EC2F)
<end of output>
Test time =   1.12 sec
----------------------------------------------------------
Test Passed.
"rotate-test" end time: Jul 21 20:34 CST
"rotate-test" time elapsed: 00:00:01
----------------------------------------------------------

21/35 Testing: matrix-test
21/35 Test: matrix-test
Command: "/Users/<USER>/work/pixman2/build/test/matrix-test"
Directory: /Users/<USER>/work/pixman2/build/test
"matrix-test" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
matrix test passed (checksum=BEBF98C3)
<end of output>
Test time =   1.72 sec
----------------------------------------------------------
Test Passed.
"matrix-test" end time: Jul 21 20:34 CST
"matrix-test" time elapsed: 00:00:01
----------------------------------------------------------

18/35 Testing: alphamap
18/35 Test: alphamap
Command: "/Users/<USER>/work/pixman2/build/test/alphamap"
Directory: /Users/<USER>/work/pixman2/build/test
"alphamap" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
<end of output>
Test time =   7.46 sec
----------------------------------------------------------
Test Passed.
"alphamap" end time: Jul 21 20:34 CST
"alphamap" time elapsed: 00:00:07
----------------------------------------------------------

20/35 Testing: pixel-test
20/35 Test: pixel-test
Command: "/Users/<USER>/work/pixman2/build/test/pixel-test"
Directory: /Users/<USER>/work/pixman2/build/test
"pixel-test" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
<end of output>
Test time =   7.98 sec
----------------------------------------------------------
Test Passed.
"pixel-test" end time: Jul 21 20:34 CST
"pixel-test" time elapsed: 00:00:07
----------------------------------------------------------

23/35 Testing: composite-traps-test
23/35 Test: composite-traps-test
Command: "/Users/<USER>/work/pixman2/build/test/composite-traps-test"
Directory: /Users/<USER>/work/pixman2/build/test
"composite-traps-test" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
composite traps test passed (checksum=AF41D210)
<end of output>
Test time =   5.94 sec
----------------------------------------------------------
Test Passed.
"composite-traps-test" end time: Jul 21 20:34 CST
"composite-traps-test" time elapsed: 00:00:05
----------------------------------------------------------

22/35 Testing: filter-reduction-test
22/35 Test: filter-reduction-test
Command: "/Users/<USER>/work/pixman2/build/test/filter-reduction-test"
Directory: /Users/<USER>/work/pixman2/build/test
"filter-reduction-test" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
filter-reduction-test passed (checksum=0x02169677)
<end of output>
Test time =  10.35 sec
----------------------------------------------------------
Test Passed.
"filter-reduction-test" end time: Jul 21 20:34 CST
"filter-reduction-test" time elapsed: 00:00:10
----------------------------------------------------------

25/35 Testing: glyph-test
25/35 Test: glyph-test
Command: "/Users/<USER>/work/pixman2/build/test/glyph-test"
Directory: /Users/<USER>/work/pixman2/build/test
"glyph-test" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
glyph test passed (checksum=FA478A79)
<end of output>
Test time =  33.72 sec
----------------------------------------------------------
Test Passed.
"glyph-test" end time: Jul 21 20:34 CST
"glyph-test" time elapsed: 00:00:33
----------------------------------------------------------

24/35 Testing: region-contains-test
24/35 Test: region-contains-test
Command: "/Users/<USER>/work/pixman2/build/test/region-contains-test"
Directory: /Users/<USER>/work/pixman2/build/test
"region-contains-test" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
region_contains test passed (checksum=548E0F3F)
<end of output>
Test time =  40.24 sec
----------------------------------------------------------
Test Passed.
"region-contains-test" end time: Jul 21 20:35 CST
"region-contains-test" time elapsed: 00:00:40
----------------------------------------------------------

27/35 Testing: stress-test
27/35 Test: stress-test
Command: "/Users/<USER>/work/pixman2/build/test/stress-test"
Directory: /Users/<USER>/work/pixman2/build/test
"stress-test" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
<end of output>
Test time =  43.64 sec
----------------------------------------------------------
Test Passed.
"stress-test" end time: Jul 21 20:35 CST
"stress-test" time elapsed: 00:00:43
----------------------------------------------------------

29/35 Testing: blitters-test
29/35 Test: blitters-test
Command: "/Users/<USER>/work/pixman2/build/test/blitters-test"
Directory: /Users/<USER>/work/pixman2/build/test
"blitters-test" start time: Jul 21 20:35 CST
Output:
----------------------------------------------------------
blitters test passed (checksum=CC21DDF0)
<end of output>
Test time =  53.87 sec
----------------------------------------------------------
Test Passed.
"blitters-test" end time: Jul 21 20:35 CST
"blitters-test" time elapsed: 00:00:53
----------------------------------------------------------

26/35 Testing: solid-test
26/35 Test: solid-test
Command: "/Users/<USER>/work/pixman2/build/test/solid-test"
Directory: /Users/<USER>/work/pixman2/build/test
"solid-test" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
solid test passed (checksum=C30FD380)
<end of output>
Test time = 105.01 sec
----------------------------------------------------------
Test Passed.
"solid-test" end time: Jul 21 20:36 CST
"solid-test" time elapsed: 00:01:45
----------------------------------------------------------

30/35 Testing: affine-test
30/35 Test: affine-test
Command: "/Users/<USER>/work/pixman2/build/test/affine-test"
Directory: /Users/<USER>/work/pixman2/build/test
"affine-test" start time: Jul 21 20:35 CST
Output:
----------------------------------------------------------
affine test passed (checksum=BE724CFE)
<end of output>
Test time =  67.00 sec
----------------------------------------------------------
Test Passed.
"affine-test" end time: Jul 21 20:36 CST
"affine-test" time elapsed: 00:01:06
----------------------------------------------------------

28/35 Testing: cover-test
28/35 Test: cover-test
Command: "/Users/<USER>/work/pixman2/build/test/cover-test"
Directory: /Users/<USER>/work/pixman2/build/test
"cover-test" start time: Jul 21 20:34 CST
Output:
----------------------------------------------------------
<end of output>
Test time = 120.05 sec
----------------------------------------------------------
Test Failed.
"cover-test" end time: Jul 21 20:36 CST
"cover-test" time elapsed: 00:02:00
----------------------------------------------------------

34/35 Testing: neg-stride-test
34/35 Test: neg-stride-test
Command: "/Users/<USER>/work/pixman2/build/test/neg-stride-test"
Directory: /Users/<USER>/work/pixman2/build/test
"neg-stride-test" start time: Jul 21 20:36 CST
Output:
----------------------------------------------------------
<end of output>
Test time =   0.00 sec
----------------------------------------------------------
Test Passed.
"neg-stride-test" end time: Jul 21 20:36 CST
"neg-stride-test" time elapsed: 00:00:00
----------------------------------------------------------

35/35 Testing: thread-test
35/35 Test: thread-test
Command: "/Users/<USER>/work/pixman2/build/test/thread-test"
Directory: /Users/<USER>/work/pixman2/build/test
"thread-test" start time: Jul 21 20:36 CST
Output:
----------------------------------------------------------
<end of output>
Test time =   0.18 sec
----------------------------------------------------------
Test Passed.
"thread-test" end time: Jul 21 20:36 CST
"thread-test" time elapsed: 00:00:00
----------------------------------------------------------

31/35 Testing: scaling-test
31/35 Test: scaling-test
Command: "/Users/<USER>/work/pixman2/build/test/scaling-test"
Directory: /Users/<USER>/work/pixman2/build/test
"scaling-test" start time: Jul 21 20:35 CST
Output:
----------------------------------------------------------
scaling test passed (checksum=92E0F068)
<end of output>
Test time =  85.45 sec
----------------------------------------------------------
Test Passed.
"scaling-test" end time: Jul 21 20:37 CST
"scaling-test" time elapsed: 00:01:25
----------------------------------------------------------

32/35 Testing: composite
32/35 Test: composite
Command: "/Users/<USER>/work/pixman2/build/test/composite"
Directory: /Users/<USER>/work/pixman2/build/test
"composite" start time: Jul 21 20:36 CST
Output:
----------------------------------------------------------
<end of output>
Test time = 120.03 sec
----------------------------------------------------------
Test Failed.
"composite" end time: Jul 21 20:38 CST
"composite" time elapsed: 00:02:00
----------------------------------------------------------

33/35 Testing: tolerance-test
33/35 Test: tolerance-test
Command: "/Users/<USER>/work/pixman2/build/test/tolerance-test"
Directory: /Users/<USER>/work/pixman2/build/test
"tolerance-test" start time: Jul 21 20:36 CST
Output:
----------------------------------------------------------
<end of output>
Test time = 120.03 sec
----------------------------------------------------------
Test Failed.
"tolerance-test" end time: Jul 21 20:38 CST
"tolerance-test" time elapsed: 00:02:00
----------------------------------------------------------

End testing: Jul 21 20:38 CST
