# CMake generated Testfile for 
# Source directory: /Users/<USER>/work/pixman2/test
# Build directory: /Users/<USER>/work/pixman2/build/test
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(oob-test "/Users/<USER>/work/pixman2/build/test/oob-test")
set_tests_properties(oob-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(infinite-loop "/Users/<USER>/work/pixman2/build/test/infinite-loop")
set_tests_properties(infinite-loop PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(trap-crasher "/Users/<USER>/work/pixman2/build/test/trap-crasher")
set_tests_properties(trap-crasher PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(fence-image-self-test "/Users/<USER>/work/pixman2/build/test/fence-image-self-test")
set_tests_properties(fence-image-self-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(region-translate-test "/Users/<USER>/work/pixman2/build/test/region-translate-test")
set_tests_properties(region-translate-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(fetch-test "/Users/<USER>/work/pixman2/build/test/fetch-test")
set_tests_properties(fetch-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(a1-trap-test "/Users/<USER>/work/pixman2/build/test/a1-trap-test")
set_tests_properties(a1-trap-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(prng-test "/Users/<USER>/work/pixman2/build/test/prng-test")
set_tests_properties(prng-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(radial-invalid "/Users/<USER>/work/pixman2/build/test/radial-invalid")
set_tests_properties(radial-invalid PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(pdf-op-test "/Users/<USER>/work/pixman2/build/test/pdf-op-test")
set_tests_properties(pdf-op-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(region-test "/Users/<USER>/work/pixman2/build/test/region-test")
set_tests_properties(region-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(region-fractional-test "/Users/<USER>/work/pixman2/build/test/region-fractional-test")
set_tests_properties(region-fractional-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(combiner-test "/Users/<USER>/work/pixman2/build/test/combiner-test")
set_tests_properties(combiner-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(scaling-crash-test "/Users/<USER>/work/pixman2/build/test/scaling-crash-test")
set_tests_properties(scaling-crash-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(alpha-loop "/Users/<USER>/work/pixman2/build/test/alpha-loop")
set_tests_properties(alpha-loop PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(scaling-helpers-test "/Users/<USER>/work/pixman2/build/test/scaling-helpers-test")
set_tests_properties(scaling-helpers-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(rotate-test "/Users/<USER>/work/pixman2/build/test/rotate-test")
set_tests_properties(rotate-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(alphamap "/Users/<USER>/work/pixman2/build/test/alphamap")
set_tests_properties(alphamap PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(gradient-crash-test "/Users/<USER>/work/pixman2/build/test/gradient-crash-test")
set_tests_properties(gradient-crash-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(pixel-test "/Users/<USER>/work/pixman2/build/test/pixel-test")
set_tests_properties(pixel-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(matrix-test "/Users/<USER>/work/pixman2/build/test/matrix-test")
set_tests_properties(matrix-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(filter-reduction-test "/Users/<USER>/work/pixman2/build/test/filter-reduction-test")
set_tests_properties(filter-reduction-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(composite-traps-test "/Users/<USER>/work/pixman2/build/test/composite-traps-test")
set_tests_properties(composite-traps-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(region-contains-test "/Users/<USER>/work/pixman2/build/test/region-contains-test")
set_tests_properties(region-contains-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(glyph-test "/Users/<USER>/work/pixman2/build/test/glyph-test")
set_tests_properties(glyph-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(solid-test "/Users/<USER>/work/pixman2/build/test/solid-test")
set_tests_properties(solid-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(stress-test "/Users/<USER>/work/pixman2/build/test/stress-test")
set_tests_properties(stress-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(cover-test "/Users/<USER>/work/pixman2/build/test/cover-test")
set_tests_properties(cover-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(blitters-test "/Users/<USER>/work/pixman2/build/test/blitters-test")
set_tests_properties(blitters-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(affine-test "/Users/<USER>/work/pixman2/build/test/affine-test")
set_tests_properties(affine-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(scaling-test "/Users/<USER>/work/pixman2/build/test/scaling-test")
set_tests_properties(scaling-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(composite "/Users/<USER>/work/pixman2/build/test/composite")
set_tests_properties(composite PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(tolerance-test "/Users/<USER>/work/pixman2/build/test/tolerance-test")
set_tests_properties(tolerance-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(neg-stride-test "/Users/<USER>/work/pixman2/build/test/neg-stride-test")
set_tests_properties(neg-stride-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
add_test(thread-test "/Users/<USER>/work/pixman2/build/test/thread-test")
set_tests_properties(thread-test PROPERTIES  TIMEOUT "120" _BACKTRACE_TRIPLES "/Users/<USER>/work/pixman2/test/CMakeLists.txt;100;add_test;/Users/<USER>/work/pixman2/test/CMakeLists.txt;0;")
