
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:24 (project)"
    message: |
      The system is: Darwin - 21.6.0 - x86_64
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:24 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /usr/bin/clang 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"
      
      The C compiler identification is AppleClang, found in:
        /Users/<USER>/work/pixman2/build/CMakeFiles/4.0.2/CompilerIdC/a.out
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerId.cmake:290 (message)"
      - "/usr/local/share/cmake/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:24 (project)"
    message: |
      Detecting C compiler apple sysroot: "/usr/bin/clang" "-E" "apple-sdk.c"
        # 1 "apple-sdk.c"
        # 1 "<built-in>" 1
        # 1 "<built-in>" 3
        # 370 "<built-in>" 3
        # 1 "<command line>" 1
        # 1 "<built-in>" 2
        # 1 "apple-sdk.c" 2
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 1 3 4
        # 242 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 1 3 4
        # 165 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityVersions.h" 1 3 4
        # 166 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 1 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 1 3 4
        # 167 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 243 "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 2 "apple-sdk.c" 2
        
        
      Found apple sysroot: /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:24 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-nqbHX3"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-nqbHX3"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-nqbHX3'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_fc63c
        [1/2] /usr/bin/clang   -v -Wl,-v -MD -MT CMakeFiles/cmTC_fc63c.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_fc63c.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_fc63c.dir/CMakeCCompilerABI.c.o -c /usr/local/share/cmake/Modules/CMakeCCompilerABI.c
        Apple clang version 14.0.0 (clang-1400.0.29.202)
        Target: x86_64-apple-darwin21.6.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
        clang: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple x86_64-apple-macosx12.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all --mrelax-relocations -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -fno-rounding-math -funwind-tables=2 -target-sdk-version=13.1 -fvisibility-inlines-hidden-static-local-var -target-cpu penryn -tune-cpu generic -debugger-tuning=lldb -target-linker-version 820.1 -v -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0 -dependency-file CMakeFiles/cmTC_fc63c.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_fc63c.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -Wno-cast-function-type -Wno-bitwise-instead-of-logical -fdebug-compilation-dir=/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-nqbHX3 -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -clang-vendor-feature=+messageToSelfInClassMethodIdReturnType -clang-vendor-feature=+disableInferNewAvailabilityFromInit -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -mllvm -disable-aligned-alloc-awareness=1 -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_fc63c.dir/CMakeCCompilerABI.c.o -x c /usr/local/share/cmake/Modules/CMakeCCompilerABI.c
        clang -cc1 version 14.0.0 (clang-1400.0.29.202) default target x86_64-apple-darwin21.6.0
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include"
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/local/include
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)
        End of search list.
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names -v -Wl,-v CMakeFiles/cmTC_fc63c.dir/CMakeCCompilerABI.c.o -o cmTC_fc63c   && :
        Apple clang version 14.0.0 (clang-1400.0.29.202)
        Target: x86_64-apple-darwin21.6.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch x86_64 -platform_version macos 12.0.0 13.1 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -o cmTC_fc63c -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_fc63c.dir/CMakeCCompilerABI.c.o -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a
        @(#)PROGRAM:ld  PROJECT:ld64-820.1
        BUILD 20:07:01 Nov  7 2022
        configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
        Library search paths:
        	/usr/local/lib
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib
        Framework search paths:
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:122 (message)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:24 (project)"
    message: |
      Effective list of requested architectures (possibly empty)  : ""
      Effective list of architectures found in the ABI info binary: "x86_64"
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:191 (message)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:24 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/local/include]
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include]
          add: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        end of search list found
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        implicit include dirs: [/usr/local/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:24 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-nqbHX3']
        ignore line: []
        ignore line: [Run Build Command(s): /usr/local/bin/ninja -v cmTC_fc63c]
        ignore line: [[1/2] /usr/bin/clang   -v -Wl -v -MD -MT CMakeFiles/cmTC_fc63c.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_fc63c.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_fc63c.dir/CMakeCCompilerABI.c.o -c /usr/local/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [Apple clang version 14.0.0 (clang-1400.0.29.202)]
        ignore line: [Target: x86_64-apple-darwin21.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        ignore line: [clang: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
        ignore line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple x86_64-apple-macosx12.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all --mrelax-relocations -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=all -fno-strict-return -fno-rounding-math -funwind-tables=2 -target-sdk-version=13.1 -fvisibility-inlines-hidden-static-local-var -target-cpu penryn -tune-cpu generic -debugger-tuning=lldb -target-linker-version 820.1 -v -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0 -dependency-file CMakeFiles/cmTC_fc63c.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_fc63c.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -Wno-cast-function-type -Wno-bitwise-instead-of-logical -fdebug-compilation-dir=/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-nqbHX3 -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -clang-vendor-feature=+messageToSelfInClassMethodIdReturnType -clang-vendor-feature=+disableInferNewAvailabilityFromInit -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -mllvm -disable-aligned-alloc-awareness=1 -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_fc63c.dir/CMakeCCompilerABI.c.o -x c /usr/local/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [clang -cc1 version 14.0.0 (clang-1400.0.29.202) default target x86_64-apple-darwin21.6.0]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/Library/Frameworks"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/local/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)]
        ignore line: [End of search list.]
        ignore line: [[2/2] : && /usr/bin/clang  -Wl -search_paths_first -Wl -headerpad_max_install_names -v -Wl -v CMakeFiles/cmTC_fc63c.dir/CMakeCCompilerABI.c.o -o cmTC_fc63c   && :]
        ignore line: [Apple clang version 14.0.0 (clang-1400.0.29.202)]
        ignore line: [Target: x86_64-apple-darwin21.6.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        link line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch x86_64 -platform_version macos 12.0.0 13.1 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk -o cmTC_fc63c -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_fc63c.dir/CMakeCCompilerABI.c.o -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld] ==> ignore
          arg [-demangle] ==> ignore
          arg [-lto_library] ==> ignore, skip following value
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib] ==> skip value of -lto_library
          arg [-dynamic] ==> ignore
          arg [-arch] ==> ignore
          arg [x86_64] ==> ignore
          arg [-platform_version] ==> ignore
          arg [macos] ==> ignore
          arg [12.0.0] ==> ignore
          arg [13.1] ==> ignore
          arg [-syslibroot] ==> ignore
          arg [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_fc63c] ==> ignore
          arg [-L/usr/local/lib] ==> dir [/usr/local/lib]
          arg [-search_paths_first] ==> ignore
          arg [-headerpad_max_install_names] ==> ignore
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_fc63c.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-lSystem] ==> lib [System]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a] ==> lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a]
        linker tool for 'C': /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld
        Library search paths: [;/usr/local/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib]
        Framework search paths: [;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/]
        remove lib [System]
        remove lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/14.0.0/lib/darwin/libclang_rt.osx.a]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib]
        collapse framework dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks]
        implicit libs: []
        implicit objs: []
        implicit dirs: [/usr/local/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib]
        implicit fwks: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/usr/local/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "/usr/local/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:24 (project)"
    message: |
      Running the C compiler's linker: "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" "-v"
      @(#)PROGRAM:ld  PROJECT:ld64-820.1
      BUILD 20:07:01 Nov  7 2022
      configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
      LTO support using: LLVM version 14.0.0, (clang-1400.0.29.202) (static support for 29, runtime is 29)
      TAPI support using: Apple TAPI version 14.0.0 (tapi-1400.0.11)
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "/usr/local/share/cmake/Modules/FindThreads.cmake:97 (check_c_source_compiles)"
      - "/usr/local/share/cmake/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "CMakeLists.txt:100 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-Vm2m0Z"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-Vm2m0Z"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-Vm2m0Z'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_580dc
        [1/2] /usr/bin/clang -DCMAKE_HAVE_LIBC_PTHREAD  -std=gnu99 -MD -MT CMakeFiles/cmTC_580dc.dir/src.c.o -MF CMakeFiles/cmTC_580dc.dir/src.c.o.d -o CMakeFiles/cmTC_580dc.dir/src.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-Vm2m0Z/src.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_580dc.dir/src.c.o -o cmTC_580dc   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:250 (try_compile)"
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:557 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:111 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-si3LHf"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-si3LHf"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-si3LHf'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_20c88
        [1/2] /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_20c88.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_20c88.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_20c88.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-si3LHf/OpenMPTryFlag.c
        FAILED: CMakeFiles/cmTC_20c88.dir/OpenMPTryFlag.c.o 
        /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_20c88.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_20c88.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_20c88.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-si3LHf/OpenMPTryFlag.c
        /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-si3LHf/OpenMPTryFlag.c:2:10: fatal error: 'omp.h' file not found
        #include <omp.h>
                 ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "CMakeLists.txt:137 (check_include_file)"
    checks:
      - "Looking for sys/mman.h"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-1PxF3T"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-1PxF3T"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SYS_MMAN_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-1PxF3T'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_becde
        [1/2] /usr/bin/clang   -std=gnu99 -MD -MT CMakeFiles/cmTC_becde.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_becde.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_becde.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-1PxF3T/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_becde.dir/CheckIncludeFile.c.o -o cmTC_becde   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "CMakeLists.txt:138 (check_include_file)"
    checks:
      - "Looking for fenv.h"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-t1HXa3"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-t1HXa3"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FENV_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-t1HXa3'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_fc2dc
        [1/2] /usr/bin/clang   -std=gnu99 -MD -MT CMakeFiles/cmTC_fc2dc.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_fc2dc.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_fc2dc.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-t1HXa3/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_fc2dc.dir/CheckIncludeFile.c.o -o cmTC_fc2dc   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "CMakeLists.txt:139 (check_include_file)"
    checks:
      - "Looking for unistd.h"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-Kqf1tc"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-Kqf1tc"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_UNISTD_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-Kqf1tc'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_1049f
        [1/2] /usr/bin/clang   -std=gnu99 -MD -MT CMakeFiles/cmTC_1049f.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_1049f.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_1049f.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-Kqf1tc/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_1049f.dir/CheckIncludeFile.c.o -o cmTC_1049f   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "CMakeLists.txt:142 (check_function_exists)"
    checks:
      - "Looking for sigaction"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-PMSJyR"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-PMSJyR"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SIGACTION"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-PMSJyR'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_23c16
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=sigaction -std=gnu99 -MD -MT CMakeFiles/cmTC_23c16.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_23c16.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_23c16.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-PMSJyR/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=sigaction -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_23c16.dir/CheckFunctionExists.c.o -o cmTC_23c16   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "CMakeLists.txt:143 (check_function_exists)"
    checks:
      - "Looking for alarm"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-6R35PD"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-6R35PD"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_ALARM"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-6R35PD'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_39913
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=alarm -std=gnu99 -MD -MT CMakeFiles/cmTC_39913.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_39913.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_39913.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-6R35PD/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=alarm -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_39913.dir/CheckFunctionExists.c.o -o cmTC_39913   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "CMakeLists.txt:144 (check_function_exists)"
    checks:
      - "Looking for mprotect"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-36aXK9"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-36aXK9"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_MPROTECT"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-36aXK9'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_5fa73
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=mprotect -std=gnu99 -MD -MT CMakeFiles/cmTC_5fa73.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_5fa73.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_5fa73.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-36aXK9/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=mprotect -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_5fa73.dir/CheckFunctionExists.c.o -o cmTC_5fa73   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "CMakeLists.txt:145 (check_function_exists)"
    checks:
      - "Looking for getpagesize"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-lKqvgY"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-lKqvgY"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETPAGESIZE"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-lKqvgY'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_752d7
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=getpagesize -std=gnu99 -MD -MT CMakeFiles/cmTC_752d7.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_752d7.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_752d7.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-lKqvgY/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=getpagesize -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_752d7.dir/CheckFunctionExists.c.o -o cmTC_752d7   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "CMakeLists.txt:146 (check_function_exists)"
    checks:
      - "Looking for mmap"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-byHHli"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-byHHli"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_MMAP"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-byHHli'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_0b703
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=mmap -std=gnu99 -MD -MT CMakeFiles/cmTC_0b703.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_0b703.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_0b703.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-byHHli/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=mmap -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_0b703.dir/CheckFunctionExists.c.o -o cmTC_0b703   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "CMakeLists.txt:147 (check_function_exists)"
    checks:
      - "Looking for getisax"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-o0Q9IW"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-o0Q9IW"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETISAX"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-o0Q9IW'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_43469
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=getisax -std=gnu99 -MD -MT CMakeFiles/cmTC_43469.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_43469.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_43469.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-o0Q9IW/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=getisax -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_43469.dir/CheckFunctionExists.c.o -o cmTC_43469   && :
        FAILED: cmTC_43469 
        : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=getisax -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_43469.dir/CheckFunctionExists.c.o -o cmTC_43469   && :
        Undefined symbols for architecture x86_64:
          "_getisax", referenced from:
              _main in CheckFunctionExists.c.o
        ld: symbol(s) not found for architecture x86_64
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "CMakeLists.txt:148 (check_function_exists)"
    checks:
      - "Looking for gettimeofday"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-QCcFGl"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-QCcFGl"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GETTIMEOFDAY"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-QCcFGl'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_96ceb
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=gettimeofday -std=gnu99 -MD -MT CMakeFiles/cmTC_96ceb.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_96ceb.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_96ceb.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-QCcFGl/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=gettimeofday -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_96ceb.dir/CheckFunctionExists.c.o -o cmTC_96ceb   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "CMakeLists.txt:152 (check_function_exists)"
    checks:
      - "Looking for posix_memalign"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-bFe80P"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-bFe80P"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_POSIX_MEMALIGN"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-bFe80P'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_f237b
        [1/2] /usr/bin/clang   -DCHECK_FUNCTION_EXISTS=posix_memalign -std=gnu99 -MD -MT CMakeFiles/cmTC_f237b.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_f237b.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_f237b.dir/CheckFunctionExists.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-bFe80P/CheckFunctionExists.c
        [2/2] : && /usr/bin/clang -DCHECK_FUNCTION_EXISTS=posix_memalign -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_f237b.dir/CheckFunctionExists.c.o -o cmTC_f237b   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckSymbolExists.cmake:160 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckSymbolExists.cmake:65 (__CHECK_SYMBOL_EXISTS_IMPL)"
      - "CMakeLists.txt:158 (check_symbol_exists)"
    checks:
      - "Looking for feenableexcept"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-E8kBmV"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-E8kBmV"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FEENABLEEXCEPT"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-E8kBmV'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_bbcd8
        [1/2] /usr/bin/clang   -std=gnu99 -MD -MT CMakeFiles/cmTC_bbcd8.dir/CheckSymbolExists.c.o -MF CMakeFiles/cmTC_bbcd8.dir/CheckSymbolExists.c.o.d -o CMakeFiles/cmTC_bbcd8.dir/CheckSymbolExists.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-E8kBmV/CheckSymbolExists.c
        FAILED: CMakeFiles/cmTC_bbcd8.dir/CheckSymbolExists.c.o 
        /usr/bin/clang   -std=gnu99 -MD -MT CMakeFiles/cmTC_bbcd8.dir/CheckSymbolExists.c.o -MF CMakeFiles/cmTC_bbcd8.dir/CheckSymbolExists.c.o.d -o CMakeFiles/cmTC_bbcd8.dir/CheckSymbolExists.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-E8kBmV/CheckSymbolExists.c
        /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-E8kBmV/CheckSymbolExists.c:8:19: error: use of undeclared identifier 'feenableexcept'; did you mean 'feraiseexcept'?
          return ((int*)(&feenableexcept))[argc];
                          ^~~~~~~~~~~~~~
                          feraiseexcept
        /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/fenv.h:299:12: note: 'feraiseexcept' declared here
        extern int feraiseexcept(int /* excepts */);
                   ^
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckSymbolExists.cmake:160 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckSymbolExists.cmake:65 (__CHECK_SYMBOL_EXISTS_IMPL)"
      - "CMakeLists.txt:162 (check_symbol_exists)"
    checks:
      - "Looking for FE_DIVBYZERO"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-RxSydu"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-RxSydu"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FEDIVBYZERO"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-RxSydu'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_5ca31
        [1/2] /usr/bin/clang   -std=gnu99 -MD -MT CMakeFiles/cmTC_5ca31.dir/CheckSymbolExists.c.o -MF CMakeFiles/cmTC_5ca31.dir/CheckSymbolExists.c.o.d -o CMakeFiles/cmTC_5ca31.dir/CheckSymbolExists.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-RxSydu/CheckSymbolExists.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_5ca31.dir/CheckSymbolExists.c.o -o cmTC_5ca31   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "CMakeLists.txt:166 (check_include_file)"
    checks:
      - "Looking for pthread.h"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-IPKOZc"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-IPKOZc"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_PTHREAD_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-IPKOZc'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_b88cb
        [1/2] /usr/bin/clang   -std=gnu99 -MD -MT CMakeFiles/cmTC_b88cb.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_b88cb.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_b88cb.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-IPKOZc/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_b88cb.dir/CheckIncludeFile.c.o -o cmTC_b88cb   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "CMakeLists.txt:187 (check_c_source_compiles)"
    checks:
      - "Performing Test HAVE_BUILTIN_CLZ"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-dmlJPr"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-dmlJPr"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_BUILTIN_CLZ"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-dmlJPr'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_71c09
        [1/2] /usr/bin/clang -DHAVE_BUILTIN_CLZ  -std=gnu99 -MD -MT CMakeFiles/cmTC_71c09.dir/src.c.o -MF CMakeFiles/cmTC_71c09.dir/src.c.o.d -o CMakeFiles/cmTC_71c09.dir/src.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-dmlJPr/src.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_71c09.dir/src.c.o -o cmTC_71c09   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "CMakeLists.txt:194 (check_c_source_compiles)"
    checks:
      - "Performing Test HAVE_GCC_VECTOR_EXTENSIONS"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-D2mAp5"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-D2mAp5"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_GCC_VECTOR_EXTENSIONS"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-D2mAp5'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_241f3
        [1/2] /usr/bin/clang -DHAVE_GCC_VECTOR_EXTENSIONS  -std=gnu99 -MD -MT CMakeFiles/cmTC_241f3.dir/src.c.o -MF CMakeFiles/cmTC_241f3.dir/src.c.o.d -o CMakeFiles/cmTC_241f3.dir/src.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-D2mAp5/src.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_241f3.dir/src.c.o -o cmTC_241f3   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "CMakeLists.txt:203 (check_c_source_compiles)"
    checks:
      - "Performing Test HAVE_FLOAT128"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-I6OqVe"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-I6OqVe"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_FLOAT128"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-I6OqVe'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_28831
        [1/2] /usr/bin/clang -DHAVE_FLOAT128  -std=gnu99 -MD -MT CMakeFiles/cmTC_28831.dir/src.c.o -MF CMakeFiles/cmTC_28831.dir/src.c.o.d -o CMakeFiles/cmTC_28831.dir/src.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-I6OqVe/src.c
        FAILED: CMakeFiles/cmTC_28831.dir/src.c.o 
        /usr/bin/clang -DHAVE_FLOAT128  -std=gnu99 -MD -MT CMakeFiles/cmTC_28831.dir/src.c.o -MF CMakeFiles/cmTC_28831.dir/src.c.o.d -o CMakeFiles/cmTC_28831.dir/src.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-I6OqVe/src.c
        /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-I6OqVe/src.c:2:5: error: __float128 is not supported on this target
            __float128 a = 1.0Q, b = 2.0Q;
            ^
        /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-I6OqVe/src.c:2:5: error: __float128 is not supported on this target
        2 errors generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_run-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceRuns.cmake:95 (try_run)"
      - "/usr/local/share/cmake/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "CMakeLists.txt:211 (check_c_source_runs)"
    checks:
      - "Performing Test TOOLCHAIN_SUPPORTS_ATTRIBUTE_CONSTRUCTOR"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-ijHM3w"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-ijHM3w"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "TOOLCHAIN_SUPPORTS_ATTRIBUTE_CONSTRUCTOR_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-ijHM3w'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_6149e
        [1/2] /usr/bin/clang -DTOOLCHAIN_SUPPORTS_ATTRIBUTE_CONSTRUCTOR  -std=gnu99 -MD -MT CMakeFiles/cmTC_6149e.dir/src.c.o -MF CMakeFiles/cmTC_6149e.dir/src.c.o.d -o CMakeFiles/cmTC_6149e.dir/src.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-ijHM3w/src.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_6149e.dir/src.c.o -o cmTC_6149e   && :
        
      exitCode: 0
    runResult:
      variable: "TOOLCHAIN_SUPPORTS_ATTRIBUTE_CONSTRUCTOR_EXITCODE"
      cached: true
      stdout: |
      stderr: |
      exitCode: 0
  -
    kind: "try_run-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceRuns.cmake:95 (try_run)"
      - "/usr/local/share/cmake/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "CMakeLists.txt:217 (check_c_source_runs)"
    checks:
      - "Performing Test TOOLCHAIN_SUPPORTS_ATTRIBUTE_DESTRUCTOR"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-xRRC4T"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-xRRC4T"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "TOOLCHAIN_SUPPORTS_ATTRIBUTE_DESTRUCTOR_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-xRRC4T'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_cf13f
        [1/2] /usr/bin/clang -DTOOLCHAIN_SUPPORTS_ATTRIBUTE_DESTRUCTOR  -std=gnu99 -MD -MT CMakeFiles/cmTC_cf13f.dir/src.c.o -MF CMakeFiles/cmTC_cf13f.dir/src.c.o.d -o CMakeFiles/cmTC_cf13f.dir/src.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-xRRC4T/src.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_cf13f.dir/src.c.o -o cmTC_cf13f   && :
        
      exitCode: 0
    runResult:
      variable: "TOOLCHAIN_SUPPORTS_ATTRIBUTE_DESTRUCTOR_EXITCODE"
      cached: true
      stdout: |
      stderr: |
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:318 (check_include_file)"
      - "CMakeLists.txt:227 (check_type_size)"
    checks:
      - "Looking for sys/types.h"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-PkYJZE"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-PkYJZE"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SYS_TYPES_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-PkYJZE'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_5e60b
        [1/2] /usr/bin/clang   -std=gnu99 -MD -MT CMakeFiles/cmTC_5e60b.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_5e60b.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_5e60b.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-PkYJZE/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_5e60b.dir/CheckIncludeFile.c.o -o cmTC_5e60b   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:319 (check_include_file)"
      - "CMakeLists.txt:227 (check_type_size)"
    checks:
      - "Looking for stdint.h"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-LJw4ie"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-LJw4ie"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STDINT_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-LJw4ie'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_bdc27
        [1/2] /usr/bin/clang   -std=gnu99 -MD -MT CMakeFiles/cmTC_bdc27.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_bdc27.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_bdc27.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-LJw4ie/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_bdc27.dir/CheckIncludeFile.c.o -o cmTC_bdc27   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:320 (check_include_file)"
      - "CMakeLists.txt:227 (check_type_size)"
    checks:
      - "Looking for stddef.h"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-7RPnGF"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-7RPnGF"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_STDDEF_H"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-7RPnGF'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_c186d
        [1/2] /usr/bin/clang   -std=gnu99 -MD -MT CMakeFiles/cmTC_c186d.dir/CheckIncludeFile.c.o -MF CMakeFiles/cmTC_c186d.dir/CheckIncludeFile.c.o.d -o CMakeFiles/cmTC_c186d.dir/CheckIncludeFile.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-7RPnGF/CheckIncludeFile.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_c186d.dir/CheckIncludeFile.c.o -o cmTC_c186d   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:212 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckTypeSize.cmake:339 (__check_type_size_impl)"
      - "CMakeLists.txt:227 (check_type_size)"
    checks:
      - "Check size of long"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-L9SiW6"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-L9SiW6"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "HAVE_SIZEOF_LONG"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-L9SiW6'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_2b403
        [1/2] /usr/bin/clang   -std=gnu99 -MD -MT CMakeFiles/cmTC_2b403.dir/SIZEOF_LONG.c.o -MF CMakeFiles/cmTC_2b403.dir/SIZEOF_LONG.c.o.d -o CMakeFiles/cmTC_2b403.dir/SIZEOF_LONG.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-L9SiW6/SIZEOF_LONG.c
        [2/2] : && /usr/bin/clang  -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_2b403.dir/SIZEOF_LONG.c.o -o cmTC_2b403   && :
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:250 (try_compile)"
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:557 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:111 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-WlnR1H"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-WlnR1H"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-WlnR1H'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_b4d19
        [1/2] /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_b4d19.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_b4d19.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_b4d19.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-WlnR1H/OpenMPTryFlag.c
        FAILED: CMakeFiles/cmTC_b4d19.dir/OpenMPTryFlag.c.o 
        /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_b4d19.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_b4d19.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_b4d19.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-WlnR1H/OpenMPTryFlag.c
        /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-WlnR1H/OpenMPTryFlag.c:2:10: fatal error: 'omp.h' file not found
        #include <omp.h>
                 ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "CMakeLists.txt:272 (check_c_source_compiles)"
    checks:
      - "Performing Test ASM_HAVE_FUNC_DIRECTIVE"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-dYsdvk"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-dYsdvk"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "ASM_HAVE_FUNC_DIRECTIVE"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-dYsdvk'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_b25c5
        [1/2] /usr/bin/clang -DASM_HAVE_FUNC_DIRECTIVE  -std=gnu99 -MD -MT CMakeFiles/cmTC_b25c5.dir/src.c.o -MF CMakeFiles/cmTC_b25c5.dir/src.c.o.d -o CMakeFiles/cmTC_b25c5.dir/src.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-dYsdvk/src.c
        FAILED: CMakeFiles/cmTC_b25c5.dir/src.c.o 
        /usr/bin/clang -DASM_HAVE_FUNC_DIRECTIVE  -std=gnu99 -MD -MT CMakeFiles/cmTC_b25c5.dir/src.c.o -MF CMakeFiles/cmTC_b25c5.dir/src.c.o.d -o CMakeFiles/cmTC_b25c5.dir/src.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-dYsdvk/src.c
        <inline asm>:1:1: error: unknown directive
        .func meson_test.endfunc
        ^
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "CMakeLists.txt:280 (check_c_source_compiles)"
    checks:
      - "Performing Test ASM_HAVE_SYNTAX_UNIFIED"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-RvWOIr"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-RvWOIr"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "ASM_HAVE_SYNTAX_UNIFIED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-RvWOIr'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_4d849
        [1/2] /usr/bin/clang -DASM_HAVE_SYNTAX_UNIFIED  -std=gnu99 -MD -MT CMakeFiles/cmTC_4d849.dir/src.c.o -MF CMakeFiles/cmTC_4d849.dir/src.c.o.d -o CMakeFiles/cmTC_4d849.dir/src.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-RvWOIr/src.c
        FAILED: CMakeFiles/cmTC_4d849.dir/src.c.o 
        /usr/bin/clang -DASM_HAVE_SYNTAX_UNIFIED  -std=gnu99 -MD -MT CMakeFiles/cmTC_4d849.dir/src.c.o -MF CMakeFiles/cmTC_4d849.dir/src.c.o.d -o CMakeFiles/cmTC_4d849.dir/src.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-RvWOIr/src.c
        /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-RvWOIr/src.c:3:5: warning: missing terminating '"' character [-Winvalid-pp-token]
            ".syntax unified
            ^
        /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-RvWOIr/src.c:3:5: error: expected string literal in 'asm'
        /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-RvWOIr/src.c:4:1: warning: missing terminating '"' character [-Winvalid-pp-token]
        "
        ^
        2 warnings and 1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_run-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/Internal/CheckSourceRuns.cmake:95 (try_run)"
      - "/usr/local/share/cmake/Modules/CheckCSourceRuns.cmake:52 (cmake_check_source_runs)"
      - "CMakeLists.txt:287 (check_c_source_runs)"
    checks:
      - "Performing Test ASM_LEADING_UNDERSCORE"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-NH7cHO"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-NH7cHO"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "ASM_LEADING_UNDERSCORE_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-NH7cHO'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_d594d
        [1/2] /usr/bin/clang -DASM_LEADING_UNDERSCORE  -std=gnu99 -MD -MT CMakeFiles/cmTC_d594d.dir/src.c.o -MF CMakeFiles/cmTC_d594d.dir/src.c.o.d -o CMakeFiles/cmTC_d594d.dir/src.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-NH7cHO/src.c
        FAILED: CMakeFiles/cmTC_d594d.dir/src.c.o 
        /usr/bin/clang -DASM_LEADING_UNDERSCORE  -std=gnu99 -MD -MT CMakeFiles/cmTC_d594d.dir/src.c.o -MF CMakeFiles/cmTC_d594d.dir/src.c.o.d -o CMakeFiles/cmTC_d594d.dir/src.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-NH7cHO/src.c
        /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-NH7cHO/src.c:5:9: warning: missing terminating '"' character [-Winvalid-pp-token]
                "   .global _testlabel
                ^
        /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-NH7cHO/src.c:5:9: error: expected string literal in 'asm'
        /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-NH7cHO/src.c:6:1: warning: missing terminating '"' character [-Winvalid-pp-token]
        "
        ^
        /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-NH7cHO/src.c:7:9: warning: missing terminating '"' character [-Winvalid-pp-token]
                "_testlabel:
                ^
        /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-NH7cHO/src.c:8:1: warning: missing terminating '"' character [-Winvalid-pp-token]
        "
        ^
        4 warnings and 1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
    runResult:
      variable: "ASM_LEADING_UNDERSCORE_EXITCODE"
      cached: true
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:250 (try_compile)"
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:557 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:111 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-CmVr6H"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-CmVr6H"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-CmVr6H'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_6f57e
        [1/2] /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_6f57e.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_6f57e.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_6f57e.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-CmVr6H/OpenMPTryFlag.c
        FAILED: CMakeFiles/cmTC_6f57e.dir/OpenMPTryFlag.c.o 
        /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_6f57e.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_6f57e.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_6f57e.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-CmVr6H/OpenMPTryFlag.c
        /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-CmVr6H/OpenMPTryFlag.c:2:10: fatal error: 'omp.h' file not found
        #include <omp.h>
                 ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:250 (try_compile)"
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:557 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:114 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-vubej5"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-vubej5"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/work/pixman2/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-vubej5'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_a07fc
        [1/2] /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_a07fc.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_a07fc.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_a07fc.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-vubej5/OpenMPTryFlag.c
        FAILED: CMakeFiles/cmTC_a07fc.dir/OpenMPTryFlag.c.o 
        /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_a07fc.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_a07fc.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_a07fc.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-vubej5/OpenMPTryFlag.c
        /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-vubej5/OpenMPTryFlag.c:2:10: fatal error: 'omp.h' file not found
        #include <omp.h>
                 ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:250 (try_compile)"
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:557 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:114 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-R5q4uR"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-R5q4uR"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/work/pixman2/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-R5q4uR'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_c8050
        [1/2] /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_c8050.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_c8050.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_c8050.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-R5q4uR/OpenMPTryFlag.c
        FAILED: CMakeFiles/cmTC_c8050.dir/OpenMPTryFlag.c.o 
        /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_c8050.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_c8050.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_c8050.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-R5q4uR/OpenMPTryFlag.c
        /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-R5q4uR/OpenMPTryFlag.c:2:10: fatal error: 'omp.h' file not found
        #include <omp.h>
                 ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:250 (try_compile)"
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:557 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:114 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-5Zik5S"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-5Zik5S"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/work/pixman2/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-5Zik5S'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_68a2b
        [1/2] /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_68a2b.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_68a2b.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_68a2b.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-5Zik5S/OpenMPTryFlag.c
        FAILED: CMakeFiles/cmTC_68a2b.dir/OpenMPTryFlag.c.o 
        /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_68a2b.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_68a2b.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_68a2b.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-5Zik5S/OpenMPTryFlag.c
        /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-5Zik5S/OpenMPTryFlag.c:2:10: fatal error: 'omp.h' file not found
        #include <omp.h>
                 ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:250 (try_compile)"
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:557 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:114 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-TpzBGz"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-TpzBGz"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/work/pixman2/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-TpzBGz'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_ffef4
        [1/2] /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_ffef4.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_ffef4.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_ffef4.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-TpzBGz/OpenMPTryFlag.c
        FAILED: CMakeFiles/cmTC_ffef4.dir/OpenMPTryFlag.c.o 
        /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_ffef4.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_ffef4.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_ffef4.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-TpzBGz/OpenMPTryFlag.c
        /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-TpzBGz/OpenMPTryFlag.c:2:10: fatal error: 'omp.h' file not found
        #include <omp.h>
                 ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:250 (try_compile)"
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:557 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:114 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-Esm4TI"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-Esm4TI"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/work/pixman2/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-Esm4TI'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_c1ce8
        [1/2] /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_c1ce8.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_c1ce8.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_c1ce8.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-Esm4TI/OpenMPTryFlag.c
        FAILED: CMakeFiles/cmTC_c1ce8.dir/OpenMPTryFlag.c.o 
        /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_c1ce8.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_c1ce8.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_c1ce8.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-Esm4TI/OpenMPTryFlag.c
        /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-Esm4TI/OpenMPTryFlag.c:2:10: fatal error: 'omp.h' file not found
        #include <omp.h>
                 ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:250 (try_compile)"
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:557 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:114 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-pyuqtC"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-pyuqtC"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/work/pixman2/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-pyuqtC'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_748b2
        [1/2] /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_748b2.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_748b2.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_748b2.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-pyuqtC/OpenMPTryFlag.c
        FAILED: CMakeFiles/cmTC_748b2.dir/OpenMPTryFlag.c.o 
        /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_748b2.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_748b2.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_748b2.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-pyuqtC/OpenMPTryFlag.c
        /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-pyuqtC/OpenMPTryFlag.c:2:10: fatal error: 'omp.h' file not found
        #include <omp.h>
                 ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:250 (try_compile)"
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:557 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:114 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-NiSpOG"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-NiSpOG"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/work/pixman2/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-NiSpOG'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_6f8b5
        [1/2] /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_6f8b5.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_6f8b5.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_6f8b5.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-NiSpOG/OpenMPTryFlag.c
        FAILED: CMakeFiles/cmTC_6f8b5.dir/OpenMPTryFlag.c.o 
        /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_6f8b5.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_6f8b5.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_6f8b5.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-NiSpOG/OpenMPTryFlag.c
        /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-NiSpOG/OpenMPTryFlag.c:2:10: fatal error: 'omp.h' file not found
        #include <omp.h>
                 ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:250 (try_compile)"
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:557 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:114 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-g6zwQ2"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-g6zwQ2"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/work/pixman2/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-g6zwQ2'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_c23fb
        [1/2] /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_c23fb.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_c23fb.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_c23fb.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-g6zwQ2/OpenMPTryFlag.c
        FAILED: CMakeFiles/cmTC_c23fb.dir/OpenMPTryFlag.c.o 
        /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_c23fb.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_c23fb.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_c23fb.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-g6zwQ2/OpenMPTryFlag.c
        /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-g6zwQ2/OpenMPTryFlag.c:2:10: fatal error: 'omp.h' file not found
        #include <omp.h>
                 ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:250 (try_compile)"
      - "/usr/local/share/cmake/Modules/FindOpenMP.cmake:557 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:114 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-lYyKGF"
      binary: "/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-lYyKGF"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/work/pixman2/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-lYyKGF'
        
        Run Build Command(s): /usr/local/bin/ninja -v cmTC_d756a
        [1/2] /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_d756a.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_d756a.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_d756a.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-lYyKGF/OpenMPTryFlag.c
        FAILED: CMakeFiles/cmTC_d756a.dir/OpenMPTryFlag.c.o 
        /usr/bin/clang   -Xclang -fopenmp -std=gnu99 -MD -MT CMakeFiles/cmTC_d756a.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_d756a.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_d756a.dir/OpenMPTryFlag.c.o -c /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-lYyKGF/OpenMPTryFlag.c
        /Users/<USER>/work/pixman2/build/CMakeFiles/CMakeScratch/TryCompile-lYyKGF/OpenMPTryFlag.c:2:10: fatal error: 'omp.h' file not found
        #include <omp.h>
                 ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...
