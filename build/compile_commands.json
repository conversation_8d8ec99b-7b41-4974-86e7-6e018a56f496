[{"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman.c.o -c /Users/<USER>/work/pixman2/pixman/pixman.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-access.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-access.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-access.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-access.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-access-accessors.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-access-accessors.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-access-accessors.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-access-accessors.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-arm.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-arm.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-arm.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-arm.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-bits-image.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-bits-image.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-bits-image.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-bits-image.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-combine32.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-combine32.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-combine32.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-combine32.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-combine-float.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-combine-float.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-combine-float.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-combine-float.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-conical-gradient.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-conical-gradient.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-conical-gradient.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-conical-gradient.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-edge.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-edge.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-edge.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-edge.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-edge-accessors.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-edge-accessors.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-edge-accessors.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-edge-accessors.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-fast-path.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-fast-path.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-fast-path.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-fast-path.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-filter.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-filter.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-filter.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-filter.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-glyph.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-glyph.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-glyph.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-glyph.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-general.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-general.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-general.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-general.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-gradient-walker.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-gradient-walker.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-gradient-walker.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-gradient-walker.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-image.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-image.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-image.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-image.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-implementation.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-implementation.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-implementation.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-implementation.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-linear-gradient.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-linear-gradient.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-linear-gradient.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-linear-gradient.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-matrix.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-matrix.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-matrix.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-matrix.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-mips.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-mips.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-mips.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-mips.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-noop.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-noop.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-noop.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-noop.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-ppc.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-ppc.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-ppc.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-ppc.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-radial-gradient.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-radial-gradient.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-radial-gradient.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-radial-gradient.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-region16.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-region16.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-region16.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-region16.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-region32.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-region32.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-region32.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-region32.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-region64f.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-region64f.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-region64f.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-region64f.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-riscv.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-riscv.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-riscv.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-riscv.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-solid-fill.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-solid-fill.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-solid-fill.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-solid-fill.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-timer.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-timer.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-timer.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-timer.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-trap.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-trap.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-trap.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-trap.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-utils.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-utils.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-utils.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-utils.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build -g -std=gnu99 -o pixman/CMakeFiles/pixman-1.dir/pixman-x86.c.o -c /Users/<USER>/work/pixman2/pixman/pixman-x86.c", "file": "/Users/<USER>/work/pixman2/pixman/pixman-x86.c", "output": "pixman/CMakeFiles/pixman-1.dir/pixman-x86.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/test/utils -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -g -std=gnu99 -o test/utils/CMakeFiles/testutils.dir/utils.c.o -c /Users/<USER>/work/pixman2/test/utils/utils.c", "file": "/Users/<USER>/work/pixman2/test/utils/utils.c", "output": "test/utils/CMakeFiles/testutils.dir/utils.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/test/utils -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -g -std=gnu99 -o test/utils/CMakeFiles/testutils.dir/utils-prng.c.o -c /Users/<USER>/work/pixman2/test/utils/utils-prng.c", "file": "/Users/<USER>/work/pixman2/test/utils/utils-prng.c", "output": "test/utils/CMakeFiles/testutils.dir/utils-prng.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o demos/CMakeFiles/demo.dir/gtk-utils.c.o -c /Users/<USER>/work/pixman2/demos/gtk-utils.c", "file": "/Users/<USER>/work/pixman2/demos/gtk-utils.c", "output": "demos/CMakeFiles/demo.dir/gtk-utils.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o demos/CMakeFiles/gradient-test.dir/gradient-test.c.o -c /Users/<USER>/work/pixman2/demos/gradient-test.c", "file": "/Users/<USER>/work/pixman2/demos/gradient-test.c", "output": "demos/CMakeFiles/gradient-test.dir/gradient-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o demos/CMakeFiles/alpha-test.dir/alpha-test.c.o -c /Users/<USER>/work/pixman2/demos/alpha-test.c", "file": "/Users/<USER>/work/pixman2/demos/alpha-test.c", "output": "demos/CMakeFiles/alpha-test.dir/alpha-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o demos/CMakeFiles/composite-test.dir/composite-test.c.o -c /Users/<USER>/work/pixman2/demos/composite-test.c", "file": "/Users/<USER>/work/pixman2/demos/composite-test.c", "output": "demos/CMakeFiles/composite-test.dir/composite-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o demos/CMakeFiles/clip-test.dir/clip-test.c.o -c /Users/<USER>/work/pixman2/demos/clip-test.c", "file": "/Users/<USER>/work/pixman2/demos/clip-test.c", "output": "demos/CMakeFiles/clip-test.dir/clip-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o demos/CMakeFiles/trap-test.dir/trap-test.c.o -c /Users/<USER>/work/pixman2/demos/trap-test.c", "file": "/Users/<USER>/work/pixman2/demos/trap-test.c", "output": "demos/CMakeFiles/trap-test.dir/trap-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o demos/CMakeFiles/screen-test.dir/screen-test.c.o -c /Users/<USER>/work/pixman2/demos/screen-test.c", "file": "/Users/<USER>/work/pixman2/demos/screen-test.c", "output": "demos/CMakeFiles/screen-test.dir/screen-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o demos/CMakeFiles/convolution-test.dir/convolution-test.c.o -c /Users/<USER>/work/pixman2/demos/convolution-test.c", "file": "/Users/<USER>/work/pixman2/demos/convolution-test.c", "output": "demos/CMakeFiles/convolution-test.dir/convolution-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o demos/CMakeFiles/radial-test.dir/radial-test.c.o -c /Users/<USER>/work/pixman2/demos/radial-test.c", "file": "/Users/<USER>/work/pixman2/demos/radial-test.c", "output": "demos/CMakeFiles/radial-test.dir/radial-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o demos/CMakeFiles/linear-gradient.dir/linear-gradient.c.o -c /Users/<USER>/work/pixman2/demos/linear-gradient.c", "file": "/Users/<USER>/work/pixman2/demos/linear-gradient.c", "output": "demos/CMakeFiles/linear-gradient.dir/linear-gradient.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o demos/CMakeFiles/conical-test.dir/conical-test.c.o -c /Users/<USER>/work/pixman2/demos/conical-test.c", "file": "/Users/<USER>/work/pixman2/demos/conical-test.c", "output": "demos/CMakeFiles/conical-test.dir/conical-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o demos/CMakeFiles/tri-test.dir/tri-test.c.o -c /Users/<USER>/work/pixman2/demos/tri-test.c", "file": "/Users/<USER>/work/pixman2/demos/tri-test.c", "output": "demos/CMakeFiles/tri-test.dir/tri-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o demos/CMakeFiles/checkerboard.dir/checkerboard.c.o -c /Users/<USER>/work/pixman2/demos/checkerboard.c", "file": "/Users/<USER>/work/pixman2/demos/checkerboard.c", "output": "demos/CMakeFiles/checkerboard.dir/checkerboard.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o demos/CMakeFiles/srgb-test.dir/srgb-test.c.o -c /Users/<USER>/work/pixman2/demos/srgb-test.c", "file": "/Users/<USER>/work/pixman2/demos/srgb-test.c", "output": "demos/CMakeFiles/srgb-test.dir/srgb-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o demos/CMakeFiles/srgb-trap-test.dir/srgb-trap-test.c.o -c /Users/<USER>/work/pixman2/demos/srgb-trap-test.c", "file": "/Users/<USER>/work/pixman2/demos/srgb-trap-test.c", "output": "demos/CMakeFiles/srgb-trap-test.dir/srgb-trap-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o demos/CMakeFiles/scale.dir/scale.c.o -c /Users/<USER>/work/pixman2/demos/scale.c", "file": "/Users/<USER>/work/pixman2/demos/scale.c", "output": "demos/CMakeFiles/scale.dir/scale.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o demos/CMakeFiles/dither.dir/dither.c.o -c /Users/<USER>/work/pixman2/demos/dither.c", "file": "/Users/<USER>/work/pixman2/demos/dither.c", "output": "demos/CMakeFiles/dither.dir/dither.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/oob-test.dir/oob-test.c.o -c /Users/<USER>/work/pixman2/test/oob-test.c", "file": "/Users/<USER>/work/pixman2/test/oob-test.c", "output": "test/CMakeFiles/oob-test.dir/oob-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/infinite-loop.dir/infinite-loop.c.o -c /Users/<USER>/work/pixman2/test/infinite-loop.c", "file": "/Users/<USER>/work/pixman2/test/infinite-loop.c", "output": "test/CMakeFiles/infinite-loop.dir/infinite-loop.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/trap-crasher.dir/trap-crasher.c.o -c /Users/<USER>/work/pixman2/test/trap-crasher.c", "file": "/Users/<USER>/work/pixman2/test/trap-crasher.c", "output": "test/CMakeFiles/trap-crasher.dir/trap-crasher.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/fence-image-self-test.dir/fence-image-self-test.c.o -c /Users/<USER>/work/pixman2/test/fence-image-self-test.c", "file": "/Users/<USER>/work/pixman2/test/fence-image-self-test.c", "output": "test/CMakeFiles/fence-image-self-test.dir/fence-image-self-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/region-translate-test.dir/region-translate-test.c.o -c /Users/<USER>/work/pixman2/test/region-translate-test.c", "file": "/Users/<USER>/work/pixman2/test/region-translate-test.c", "output": "test/CMakeFiles/region-translate-test.dir/region-translate-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/fetch-test.dir/fetch-test.c.o -c /Users/<USER>/work/pixman2/test/fetch-test.c", "file": "/Users/<USER>/work/pixman2/test/fetch-test.c", "output": "test/CMakeFiles/fetch-test.dir/fetch-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/a1-trap-test.dir/a1-trap-test.c.o -c /Users/<USER>/work/pixman2/test/a1-trap-test.c", "file": "/Users/<USER>/work/pixman2/test/a1-trap-test.c", "output": "test/CMakeFiles/a1-trap-test.dir/a1-trap-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/prng-test.dir/prng-test.c.o -c /Users/<USER>/work/pixman2/test/prng-test.c", "file": "/Users/<USER>/work/pixman2/test/prng-test.c", "output": "test/CMakeFiles/prng-test.dir/prng-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/radial-invalid.dir/radial-invalid.c.o -c /Users/<USER>/work/pixman2/test/radial-invalid.c", "file": "/Users/<USER>/work/pixman2/test/radial-invalid.c", "output": "test/CMakeFiles/radial-invalid.dir/radial-invalid.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/pdf-op-test.dir/pdf-op-test.c.o -c /Users/<USER>/work/pixman2/test/pdf-op-test.c", "file": "/Users/<USER>/work/pixman2/test/pdf-op-test.c", "output": "test/CMakeFiles/pdf-op-test.dir/pdf-op-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/region-test.dir/region-test.c.o -c /Users/<USER>/work/pixman2/test/region-test.c", "file": "/Users/<USER>/work/pixman2/test/region-test.c", "output": "test/CMakeFiles/region-test.dir/region-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/region-fractional-test.dir/region-fractional-test.c.o -c /Users/<USER>/work/pixman2/test/region-fractional-test.c", "file": "/Users/<USER>/work/pixman2/test/region-fractional-test.c", "output": "test/CMakeFiles/region-fractional-test.dir/region-fractional-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/combiner-test.dir/combiner-test.c.o -c /Users/<USER>/work/pixman2/test/combiner-test.c", "file": "/Users/<USER>/work/pixman2/test/combiner-test.c", "output": "test/CMakeFiles/combiner-test.dir/combiner-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/scaling-crash-test.dir/scaling-crash-test.c.o -c /Users/<USER>/work/pixman2/test/scaling-crash-test.c", "file": "/Users/<USER>/work/pixman2/test/scaling-crash-test.c", "output": "test/CMakeFiles/scaling-crash-test.dir/scaling-crash-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/alpha-loop.dir/alpha-loop.c.o -c /Users/<USER>/work/pixman2/test/alpha-loop.c", "file": "/Users/<USER>/work/pixman2/test/alpha-loop.c", "output": "test/CMakeFiles/alpha-loop.dir/alpha-loop.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/scaling-helpers-test.dir/scaling-helpers-test.c.o -c /Users/<USER>/work/pixman2/test/scaling-helpers-test.c", "file": "/Users/<USER>/work/pixman2/test/scaling-helpers-test.c", "output": "test/CMakeFiles/scaling-helpers-test.dir/scaling-helpers-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/rotate-test.dir/rotate-test.c.o -c /Users/<USER>/work/pixman2/test/rotate-test.c", "file": "/Users/<USER>/work/pixman2/test/rotate-test.c", "output": "test/CMakeFiles/rotate-test.dir/rotate-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/alphamap.dir/alphamap.c.o -c /Users/<USER>/work/pixman2/test/alphamap.c", "file": "/Users/<USER>/work/pixman2/test/alphamap.c", "output": "test/CMakeFiles/alphamap.dir/alphamap.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/gradient-crash-test.dir/gradient-crash-test.c.o -c /Users/<USER>/work/pixman2/test/gradient-crash-test.c", "file": "/Users/<USER>/work/pixman2/test/gradient-crash-test.c", "output": "test/CMakeFiles/gradient-crash-test.dir/gradient-crash-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/pixel-test.dir/pixel-test.c.o -c /Users/<USER>/work/pixman2/test/pixel-test.c", "file": "/Users/<USER>/work/pixman2/test/pixel-test.c", "output": "test/CMakeFiles/pixel-test.dir/pixel-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/matrix-test.dir/matrix-test.c.o -c /Users/<USER>/work/pixman2/test/matrix-test.c", "file": "/Users/<USER>/work/pixman2/test/matrix-test.c", "output": "test/CMakeFiles/matrix-test.dir/matrix-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/filter-reduction-test.dir/filter-reduction-test.c.o -c /Users/<USER>/work/pixman2/test/filter-reduction-test.c", "file": "/Users/<USER>/work/pixman2/test/filter-reduction-test.c", "output": "test/CMakeFiles/filter-reduction-test.dir/filter-reduction-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/composite-traps-test.dir/composite-traps-test.c.o -c /Users/<USER>/work/pixman2/test/composite-traps-test.c", "file": "/Users/<USER>/work/pixman2/test/composite-traps-test.c", "output": "test/CMakeFiles/composite-traps-test.dir/composite-traps-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/region-contains-test.dir/region-contains-test.c.o -c /Users/<USER>/work/pixman2/test/region-contains-test.c", "file": "/Users/<USER>/work/pixman2/test/region-contains-test.c", "output": "test/CMakeFiles/region-contains-test.dir/region-contains-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/glyph-test.dir/glyph-test.c.o -c /Users/<USER>/work/pixman2/test/glyph-test.c", "file": "/Users/<USER>/work/pixman2/test/glyph-test.c", "output": "test/CMakeFiles/glyph-test.dir/glyph-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/solid-test.dir/solid-test.c.o -c /Users/<USER>/work/pixman2/test/solid-test.c", "file": "/Users/<USER>/work/pixman2/test/solid-test.c", "output": "test/CMakeFiles/solid-test.dir/solid-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/stress-test.dir/stress-test.c.o -c /Users/<USER>/work/pixman2/test/stress-test.c", "file": "/Users/<USER>/work/pixman2/test/stress-test.c", "output": "test/CMakeFiles/stress-test.dir/stress-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/cover-test.dir/cover-test.c.o -c /Users/<USER>/work/pixman2/test/cover-test.c", "file": "/Users/<USER>/work/pixman2/test/cover-test.c", "output": "test/CMakeFiles/cover-test.dir/cover-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/blitters-test.dir/blitters-test.c.o -c /Users/<USER>/work/pixman2/test/blitters-test.c", "file": "/Users/<USER>/work/pixman2/test/blitters-test.c", "output": "test/CMakeFiles/blitters-test.dir/blitters-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/affine-test.dir/affine-test.c.o -c /Users/<USER>/work/pixman2/test/affine-test.c", "file": "/Users/<USER>/work/pixman2/test/affine-test.c", "output": "test/CMakeFiles/affine-test.dir/affine-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/scaling-test.dir/scaling-test.c.o -c /Users/<USER>/work/pixman2/test/scaling-test.c", "file": "/Users/<USER>/work/pixman2/test/scaling-test.c", "output": "test/CMakeFiles/scaling-test.dir/scaling-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/composite.dir/composite.c.o -c /Users/<USER>/work/pixman2/test/composite.c", "file": "/Users/<USER>/work/pixman2/test/composite.c", "output": "test/CMakeFiles/composite.dir/composite.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/tolerance-test.dir/tolerance-test.c.o -c /Users/<USER>/work/pixman2/test/tolerance-test.c", "file": "/Users/<USER>/work/pixman2/test/tolerance-test.c", "output": "test/CMakeFiles/tolerance-test.dir/tolerance-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/neg-stride-test.dir/neg-stride-test.c.o -c /Users/<USER>/work/pixman2/test/neg-stride-test.c", "file": "/Users/<USER>/work/pixman2/test/neg-stride-test.c", "output": "test/CMakeFiles/neg-stride-test.dir/neg-stride-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/thread-test.dir/thread-test.c.o -c /Users/<USER>/work/pixman2/test/thread-test.c", "file": "/Users/<USER>/work/pixman2/test/thread-test.c", "output": "test/CMakeFiles/thread-test.dir/thread-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/lowlevel-blt-bench.dir/lowlevel-blt-bench.c.o -c /Users/<USER>/work/pixman2/test/lowlevel-blt-bench.c", "file": "/Users/<USER>/work/pixman2/test/lowlevel-blt-bench.c", "output": "test/CMakeFiles/lowlevel-blt-bench.dir/lowlevel-blt-bench.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/radial-perf-test.dir/radial-perf-test.c.o -c /Users/<USER>/work/pixman2/test/radial-perf-test.c", "file": "/Users/<USER>/work/pixman2/test/radial-perf-test.c", "output": "test/CMakeFiles/radial-perf-test.dir/radial-perf-test.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/check-formats.dir/check-formats.c.o -c /Users/<USER>/work/pixman2/test/check-formats.c", "file": "/Users/<USER>/work/pixman2/test/check-formats.c", "output": "test/CMakeFiles/check-formats.dir/check-formats.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/scaling-bench.dir/scaling-bench.c.o -c /Users/<USER>/work/pixman2/test/scaling-bench.c", "file": "/Users/<USER>/work/pixman2/test/scaling-bench.c", "output": "test/CMakeFiles/scaling-bench.dir/scaling-bench.c.o"}, {"directory": "/Users/<USER>/work/pixman2/build", "command": "/usr/bin/clang -DHAVE_CONFIG_H -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils -g -std=gnu99 -o test/CMakeFiles/affine-bench.dir/affine-bench.c.o -c /Users/<USER>/work/pixman2/test/affine-bench.c", "file": "/Users/<USER>/work/pixman2/test/affine-bench.c", "output": "test/CMakeFiles/affine-bench.dir/affine-bench.c.o"}]