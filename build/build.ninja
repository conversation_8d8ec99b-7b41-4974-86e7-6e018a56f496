# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.0

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: pixman
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /Users/<USER>/work/pixman2/build/

#############################################
# Utility command for test

build CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/pixman2/build && /usr/local/bin/ctest
  DESC = Running tests...
  pool = console
  restat = 1

build test: phony CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/pixman2/build && /usr/local/bin/ccmake -S/Users/<USER>/work/pixman2 -B/Users/<USER>/work/pixman2/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/pixman2/build && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/pixman2 -B/Users/<USER>/work/pixman2/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles/install.util: CUSTOM_COMMAND all
  COMMAND = cd /Users/<USER>/work/pixman2/build && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles/install.util


#############################################
# Utility command for install/local

build CMakeFiles/install/local.util: CUSTOM_COMMAND all
  COMMAND = cd /Users/<USER>/work/pixman2/build && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install/local: phony CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build CMakeFiles/install/strip.util: CUSTOM_COMMAND all
  COMMAND = cd /Users/<USER>/work/pixman2/build && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build install/strip: phony CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/work/pixman2/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target pixman-1


#############################################
# Order-only phony target for pixman-1

build cmake_object_order_depends_target_pixman-1: phony || .

build pixman/CMakeFiles/pixman-1.dir/pixman.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-access.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-access.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-access.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-access-accessors.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-access-accessors.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-access-accessors.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-arm.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-arm.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-arm.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-bits-image.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-bits-image.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-bits-image.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-combine32.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-combine32.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-combine32.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-combine-float.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-combine-float.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-combine-float.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-conical-gradient.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-conical-gradient.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-conical-gradient.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-edge.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-edge.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-edge.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-edge-accessors.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-edge-accessors.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-edge-accessors.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-fast-path.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-fast-path.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-fast-path.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-filter.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-filter.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-filter.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-glyph.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-glyph.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-glyph.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-general.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-general.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-general.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-gradient-walker.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-gradient-walker.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-gradient-walker.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-image.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-image.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-image.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-implementation.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-implementation.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-implementation.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-linear-gradient.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-linear-gradient.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-linear-gradient.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-matrix.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-matrix.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-matrix.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-mips.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-mips.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-mips.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-noop.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-noop.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-noop.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-ppc.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-ppc.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-ppc.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-radial-gradient.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-radial-gradient.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-radial-gradient.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-region16.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-region16.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-region16.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-region32.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-region32.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-region32.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-region64f.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-region64f.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-region64f.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-riscv.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-riscv.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-riscv.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-solid-fill.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-solid-fill.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-solid-fill.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-timer.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-timer.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-timer.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-trap.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-trap.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-trap.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-utils.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-utils.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-utils.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir

build pixman/CMakeFiles/pixman-1.dir/pixman-x86.c.o: C_COMPILER__pixman-1_unscanned_Debug /Users/<USER>/work/pixman2/pixman/pixman-x86.c || cmake_object_order_depends_target_pixman-1
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = pixman/CMakeFiles/pixman-1.dir/pixman-x86.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/build
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  OBJECT_FILE_DIR = pixman/CMakeFiles/pixman-1.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target pixman-1


#############################################
# Link the static library pixman/libpixman-1.a

build pixman/libpixman-1.a: C_STATIC_LIBRARY_LINKER__pixman-1_Debug pixman/CMakeFiles/pixman-1.dir/pixman.c.o pixman/CMakeFiles/pixman-1.dir/pixman-access.c.o pixman/CMakeFiles/pixman-1.dir/pixman-access-accessors.c.o pixman/CMakeFiles/pixman-1.dir/pixman-arm.c.o pixman/CMakeFiles/pixman-1.dir/pixman-bits-image.c.o pixman/CMakeFiles/pixman-1.dir/pixman-combine32.c.o pixman/CMakeFiles/pixman-1.dir/pixman-combine-float.c.o pixman/CMakeFiles/pixman-1.dir/pixman-conical-gradient.c.o pixman/CMakeFiles/pixman-1.dir/pixman-edge.c.o pixman/CMakeFiles/pixman-1.dir/pixman-edge-accessors.c.o pixman/CMakeFiles/pixman-1.dir/pixman-fast-path.c.o pixman/CMakeFiles/pixman-1.dir/pixman-filter.c.o pixman/CMakeFiles/pixman-1.dir/pixman-glyph.c.o pixman/CMakeFiles/pixman-1.dir/pixman-general.c.o pixman/CMakeFiles/pixman-1.dir/pixman-gradient-walker.c.o pixman/CMakeFiles/pixman-1.dir/pixman-image.c.o pixman/CMakeFiles/pixman-1.dir/pixman-implementation.c.o pixman/CMakeFiles/pixman-1.dir/pixman-linear-gradient.c.o pixman/CMakeFiles/pixman-1.dir/pixman-matrix.c.o pixman/CMakeFiles/pixman-1.dir/pixman-mips.c.o pixman/CMakeFiles/pixman-1.dir/pixman-noop.c.o pixman/CMakeFiles/pixman-1.dir/pixman-ppc.c.o pixman/CMakeFiles/pixman-1.dir/pixman-radial-gradient.c.o pixman/CMakeFiles/pixman-1.dir/pixman-region16.c.o pixman/CMakeFiles/pixman-1.dir/pixman-region32.c.o pixman/CMakeFiles/pixman-1.dir/pixman-region64f.c.o pixman/CMakeFiles/pixman-1.dir/pixman-riscv.c.o pixman/CMakeFiles/pixman-1.dir/pixman-solid-fill.c.o pixman/CMakeFiles/pixman-1.dir/pixman-timer.c.o pixman/CMakeFiles/pixman-1.dir/pixman-trap.c.o pixman/CMakeFiles/pixman-1.dir/pixman-utils.c.o pixman/CMakeFiles/pixman-1.dir/pixman-x86.c.o
  CONFIG = Debug
  LANGUAGE_COMPILE_FLAGS = -g
  OBJECT_DIR = pixman/CMakeFiles/pixman-1.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = pixman/libpixman-1.a
  TARGET_PDB = pixman-1.a.dbg


#############################################
# Utility command for test

build pixman/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/pixman2/build/pixman && /usr/local/bin/ctest
  DESC = Running tests...
  pool = console
  restat = 1

build pixman/test: phony pixman/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build pixman/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/pixman2/build/pixman && /usr/local/bin/ccmake -S/Users/<USER>/work/pixman2 -B/Users/<USER>/work/pixman2/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build pixman/edit_cache: phony pixman/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build pixman/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/pixman2/build/pixman && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/pixman2 -B/Users/<USER>/work/pixman2/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build pixman/rebuild_cache: phony pixman/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build pixman/list_install_components: phony


#############################################
# Utility command for install

build pixman/CMakeFiles/install.util: CUSTOM_COMMAND pixman/all
  COMMAND = cd /Users/<USER>/work/pixman2/build/pixman && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build pixman/install: phony pixman/CMakeFiles/install.util


#############################################
# Utility command for install/local

build pixman/CMakeFiles/install/local.util: CUSTOM_COMMAND pixman/all
  COMMAND = cd /Users/<USER>/work/pixman2/build/pixman && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build pixman/install/local: phony pixman/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build pixman/CMakeFiles/install/strip.util: CUSTOM_COMMAND pixman/all
  COMMAND = cd /Users/<USER>/work/pixman2/build/pixman && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build pixman/install/strip: phony pixman/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/work/pixman2/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target testutils


#############################################
# Order-only phony target for testutils

build cmake_object_order_depends_target_testutils: phony || cmake_object_order_depends_target_pixman-1

build test/utils/CMakeFiles/testutils.dir/utils.c.o: C_COMPILER__testutils_unscanned_Debug /Users/<USER>/work/pixman2/test/utils/utils.c || cmake_object_order_depends_target_testutils
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/utils/CMakeFiles/testutils.dir/utils.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/test/utils -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman
  OBJECT_DIR = test/utils/CMakeFiles/testutils.dir
  OBJECT_FILE_DIR = test/utils/CMakeFiles/testutils.dir

build test/utils/CMakeFiles/testutils.dir/utils-prng.c.o: C_COMPILER__testutils_unscanned_Debug /Users/<USER>/work/pixman2/test/utils/utils-prng.c || cmake_object_order_depends_target_testutils
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/utils/CMakeFiles/testutils.dir/utils-prng.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/test/utils -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman
  OBJECT_DIR = test/utils/CMakeFiles/testutils.dir
  OBJECT_FILE_DIR = test/utils/CMakeFiles/testutils.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target testutils


#############################################
# Link the static library test/utils/libtestutils.a

build test/utils/libtestutils.a: C_STATIC_LIBRARY_LINKER__testutils_Debug test/utils/CMakeFiles/testutils.dir/utils.c.o test/utils/CMakeFiles/testutils.dir/utils-prng.c.o || pixman/libpixman-1.a
  CONFIG = Debug
  LANGUAGE_COMPILE_FLAGS = -g
  OBJECT_DIR = test/utils/CMakeFiles/testutils.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/utils/libtestutils.a
  TARGET_PDB = testutils.a.dbg


#############################################
# Utility command for test

build test/utils/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/pixman2/build/test/utils && /usr/local/bin/ctest
  DESC = Running tests...
  pool = console
  restat = 1

build test/utils/test: phony test/utils/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build test/utils/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/pixman2/build/test/utils && /usr/local/bin/ccmake -S/Users/<USER>/work/pixman2 -B/Users/<USER>/work/pixman2/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build test/utils/edit_cache: phony test/utils/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build test/utils/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/pixman2/build/test/utils && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/pixman2 -B/Users/<USER>/work/pixman2/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build test/utils/rebuild_cache: phony test/utils/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build test/utils/list_install_components: phony


#############################################
# Utility command for install

build test/utils/CMakeFiles/install.util: CUSTOM_COMMAND test/utils/all
  COMMAND = cd /Users/<USER>/work/pixman2/build/test/utils && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build test/utils/install: phony test/utils/CMakeFiles/install.util


#############################################
# Utility command for install/local

build test/utils/CMakeFiles/install/local.util: CUSTOM_COMMAND test/utils/all
  COMMAND = cd /Users/<USER>/work/pixman2/build/test/utils && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build test/utils/install/local: phony test/utils/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build test/utils/CMakeFiles/install/strip.util: CUSTOM_COMMAND test/utils/all
  COMMAND = cd /Users/<USER>/work/pixman2/build/test/utils && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build test/utils/install/strip: phony test/utils/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/work/pixman2/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target demo


#############################################
# Order-only phony target for demo

build cmake_object_order_depends_target_demo: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build demos/CMakeFiles/demo.dir/gtk-utils.c.o: C_COMPILER__demo_unscanned_Debug /Users/<USER>/work/pixman2/demos/gtk-utils.c || cmake_object_order_depends_target_demo
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = demos/CMakeFiles/demo.dir/gtk-utils.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = demos/CMakeFiles/demo.dir
  OBJECT_FILE_DIR = demos/CMakeFiles/demo.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target demo


#############################################
# Link the static library demos/libdemo.a

build demos/libdemo.a: C_STATIC_LIBRARY_LINKER__demo_Debug demos/CMakeFiles/demo.dir/gtk-utils.c.o || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  LANGUAGE_COMPILE_FLAGS = -g
  OBJECT_DIR = demos/CMakeFiles/demo.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = demos/libdemo.a
  TARGET_PDB = demo.a.dbg

# =============================================================================
# Object build statements for EXECUTABLE target gradient-test


#############################################
# Order-only phony target for gradient-test

build cmake_object_order_depends_target_gradient-test: phony || cmake_object_order_depends_target_demo cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build demos/CMakeFiles/gradient-test.dir/gradient-test.c.o: C_COMPILER__gradient-test_unscanned_Debug /Users/<USER>/work/pixman2/demos/gradient-test.c || cmake_object_order_depends_target_gradient-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = demos/CMakeFiles/gradient-test.dir/gradient-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = demos/CMakeFiles/gradient-test.dir
  OBJECT_FILE_DIR = demos/CMakeFiles/gradient-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target gradient-test


#############################################
# Link the executable demos/gradient-test

build demos/gradient-test: C_EXECUTABLE_LINKER__gradient-test_Debug demos/CMakeFiles/gradient-test.dir/gradient-test.c.o | demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a pixman/libpixman-1.a /usr/local/lib/libpng.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = demos/libdemo.a  pixman/libpixman-1.a  test/utils/libtestutils.a  -lgtk-3  -lgdk-3  -lpangocairo-1.0  -lpango-1.0  -lharfbuzz  -lcairo-gobject  -lcairo  -lm  -ldl  -lfontconfig  -lfreetype  pixman/libpixman-1.a  -lgdk_pixbuf-2.0  -lpng16  -latk-1.0  -lgio-2.0  -lgobject-2.0  -lglib-2.0  -lintl  -lz  -framework CoreFoundation  -framework ApplicationServices  -lglib-2.0  -lintl  /usr/local/lib/libpng.dylib  -lz  -framework CoreFoundation  -framework ApplicationServices  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = demos/CMakeFiles/gradient-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = demos/gradient-test
  TARGET_PDB = gradient-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target alpha-test


#############################################
# Order-only phony target for alpha-test

build cmake_object_order_depends_target_alpha-test: phony || cmake_object_order_depends_target_demo cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build demos/CMakeFiles/alpha-test.dir/alpha-test.c.o: C_COMPILER__alpha-test_unscanned_Debug /Users/<USER>/work/pixman2/demos/alpha-test.c || cmake_object_order_depends_target_alpha-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = demos/CMakeFiles/alpha-test.dir/alpha-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = demos/CMakeFiles/alpha-test.dir
  OBJECT_FILE_DIR = demos/CMakeFiles/alpha-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target alpha-test


#############################################
# Link the executable demos/alpha-test

build demos/alpha-test: C_EXECUTABLE_LINKER__alpha-test_Debug demos/CMakeFiles/alpha-test.dir/alpha-test.c.o | demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a pixman/libpixman-1.a /usr/local/lib/libpng.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = demos/libdemo.a  pixman/libpixman-1.a  test/utils/libtestutils.a  -lgtk-3  -lgdk-3  -lpangocairo-1.0  -lpango-1.0  -lharfbuzz  -lcairo-gobject  -lcairo  -lm  -ldl  -lfontconfig  -lfreetype  pixman/libpixman-1.a  -lgdk_pixbuf-2.0  -lpng16  -latk-1.0  -lgio-2.0  -lgobject-2.0  -lglib-2.0  -lintl  -lz  -framework CoreFoundation  -framework ApplicationServices  -lglib-2.0  -lintl  /usr/local/lib/libpng.dylib  -lz  -framework CoreFoundation  -framework ApplicationServices  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = demos/CMakeFiles/alpha-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = demos/alpha-test
  TARGET_PDB = alpha-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target composite-test


#############################################
# Order-only phony target for composite-test

build cmake_object_order_depends_target_composite-test: phony || cmake_object_order_depends_target_demo cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build demos/CMakeFiles/composite-test.dir/composite-test.c.o: C_COMPILER__composite-test_unscanned_Debug /Users/<USER>/work/pixman2/demos/composite-test.c || cmake_object_order_depends_target_composite-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = demos/CMakeFiles/composite-test.dir/composite-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = demos/CMakeFiles/composite-test.dir
  OBJECT_FILE_DIR = demos/CMakeFiles/composite-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target composite-test


#############################################
# Link the executable demos/composite-test

build demos/composite-test: C_EXECUTABLE_LINKER__composite-test_Debug demos/CMakeFiles/composite-test.dir/composite-test.c.o | demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a pixman/libpixman-1.a /usr/local/lib/libpng.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = demos/libdemo.a  pixman/libpixman-1.a  test/utils/libtestutils.a  -lgtk-3  -lgdk-3  -lpangocairo-1.0  -lpango-1.0  -lharfbuzz  -lcairo-gobject  -lcairo  -lm  -ldl  -lfontconfig  -lfreetype  pixman/libpixman-1.a  -lgdk_pixbuf-2.0  -lpng16  -latk-1.0  -lgio-2.0  -lgobject-2.0  -lglib-2.0  -lintl  -lz  -framework CoreFoundation  -framework ApplicationServices  -lglib-2.0  -lintl  /usr/local/lib/libpng.dylib  -lz  -framework CoreFoundation  -framework ApplicationServices  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = demos/CMakeFiles/composite-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = demos/composite-test
  TARGET_PDB = composite-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target clip-test


#############################################
# Order-only phony target for clip-test

build cmake_object_order_depends_target_clip-test: phony || cmake_object_order_depends_target_demo cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build demos/CMakeFiles/clip-test.dir/clip-test.c.o: C_COMPILER__clip-test_unscanned_Debug /Users/<USER>/work/pixman2/demos/clip-test.c || cmake_object_order_depends_target_clip-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = demos/CMakeFiles/clip-test.dir/clip-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = demos/CMakeFiles/clip-test.dir
  OBJECT_FILE_DIR = demos/CMakeFiles/clip-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target clip-test


#############################################
# Link the executable demos/clip-test

build demos/clip-test: C_EXECUTABLE_LINKER__clip-test_Debug demos/CMakeFiles/clip-test.dir/clip-test.c.o | demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a pixman/libpixman-1.a /usr/local/lib/libpng.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = demos/libdemo.a  pixman/libpixman-1.a  test/utils/libtestutils.a  -lgtk-3  -lgdk-3  -lpangocairo-1.0  -lpango-1.0  -lharfbuzz  -lcairo-gobject  -lcairo  -lm  -ldl  -lfontconfig  -lfreetype  pixman/libpixman-1.a  -lgdk_pixbuf-2.0  -lpng16  -latk-1.0  -lgio-2.0  -lgobject-2.0  -lglib-2.0  -lintl  -lz  -framework CoreFoundation  -framework ApplicationServices  -lglib-2.0  -lintl  /usr/local/lib/libpng.dylib  -lz  -framework CoreFoundation  -framework ApplicationServices  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = demos/CMakeFiles/clip-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = demos/clip-test
  TARGET_PDB = clip-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target trap-test


#############################################
# Order-only phony target for trap-test

build cmake_object_order_depends_target_trap-test: phony || cmake_object_order_depends_target_demo cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build demos/CMakeFiles/trap-test.dir/trap-test.c.o: C_COMPILER__trap-test_unscanned_Debug /Users/<USER>/work/pixman2/demos/trap-test.c || cmake_object_order_depends_target_trap-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = demos/CMakeFiles/trap-test.dir/trap-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = demos/CMakeFiles/trap-test.dir
  OBJECT_FILE_DIR = demos/CMakeFiles/trap-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target trap-test


#############################################
# Link the executable demos/trap-test

build demos/trap-test: C_EXECUTABLE_LINKER__trap-test_Debug demos/CMakeFiles/trap-test.dir/trap-test.c.o | demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a pixman/libpixman-1.a /usr/local/lib/libpng.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = demos/libdemo.a  pixman/libpixman-1.a  test/utils/libtestutils.a  -lgtk-3  -lgdk-3  -lpangocairo-1.0  -lpango-1.0  -lharfbuzz  -lcairo-gobject  -lcairo  -lm  -ldl  -lfontconfig  -lfreetype  pixman/libpixman-1.a  -lgdk_pixbuf-2.0  -lpng16  -latk-1.0  -lgio-2.0  -lgobject-2.0  -lglib-2.0  -lintl  -lz  -framework CoreFoundation  -framework ApplicationServices  -lglib-2.0  -lintl  /usr/local/lib/libpng.dylib  -lz  -framework CoreFoundation  -framework ApplicationServices  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = demos/CMakeFiles/trap-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = demos/trap-test
  TARGET_PDB = trap-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target screen-test


#############################################
# Order-only phony target for screen-test

build cmake_object_order_depends_target_screen-test: phony || cmake_object_order_depends_target_demo cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build demos/CMakeFiles/screen-test.dir/screen-test.c.o: C_COMPILER__screen-test_unscanned_Debug /Users/<USER>/work/pixman2/demos/screen-test.c || cmake_object_order_depends_target_screen-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = demos/CMakeFiles/screen-test.dir/screen-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = demos/CMakeFiles/screen-test.dir
  OBJECT_FILE_DIR = demos/CMakeFiles/screen-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target screen-test


#############################################
# Link the executable demos/screen-test

build demos/screen-test: C_EXECUTABLE_LINKER__screen-test_Debug demos/CMakeFiles/screen-test.dir/screen-test.c.o | demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a pixman/libpixman-1.a /usr/local/lib/libpng.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = demos/libdemo.a  pixman/libpixman-1.a  test/utils/libtestutils.a  -lgtk-3  -lgdk-3  -lpangocairo-1.0  -lpango-1.0  -lharfbuzz  -lcairo-gobject  -lcairo  -lm  -ldl  -lfontconfig  -lfreetype  pixman/libpixman-1.a  -lgdk_pixbuf-2.0  -lpng16  -latk-1.0  -lgio-2.0  -lgobject-2.0  -lglib-2.0  -lintl  -lz  -framework CoreFoundation  -framework ApplicationServices  -lglib-2.0  -lintl  /usr/local/lib/libpng.dylib  -lz  -framework CoreFoundation  -framework ApplicationServices  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = demos/CMakeFiles/screen-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = demos/screen-test
  TARGET_PDB = screen-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target convolution-test


#############################################
# Order-only phony target for convolution-test

build cmake_object_order_depends_target_convolution-test: phony || cmake_object_order_depends_target_demo cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build demos/CMakeFiles/convolution-test.dir/convolution-test.c.o: C_COMPILER__convolution-test_unscanned_Debug /Users/<USER>/work/pixman2/demos/convolution-test.c || cmake_object_order_depends_target_convolution-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = demos/CMakeFiles/convolution-test.dir/convolution-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = demos/CMakeFiles/convolution-test.dir
  OBJECT_FILE_DIR = demos/CMakeFiles/convolution-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target convolution-test


#############################################
# Link the executable demos/convolution-test

build demos/convolution-test: C_EXECUTABLE_LINKER__convolution-test_Debug demos/CMakeFiles/convolution-test.dir/convolution-test.c.o | demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a pixman/libpixman-1.a /usr/local/lib/libpng.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = demos/libdemo.a  pixman/libpixman-1.a  test/utils/libtestutils.a  -lgtk-3  -lgdk-3  -lpangocairo-1.0  -lpango-1.0  -lharfbuzz  -lcairo-gobject  -lcairo  -lm  -ldl  -lfontconfig  -lfreetype  pixman/libpixman-1.a  -lgdk_pixbuf-2.0  -lpng16  -latk-1.0  -lgio-2.0  -lgobject-2.0  -lglib-2.0  -lintl  -lz  -framework CoreFoundation  -framework ApplicationServices  -lglib-2.0  -lintl  /usr/local/lib/libpng.dylib  -lz  -framework CoreFoundation  -framework ApplicationServices  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = demos/CMakeFiles/convolution-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = demos/convolution-test
  TARGET_PDB = convolution-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target radial-test


#############################################
# Order-only phony target for radial-test

build cmake_object_order_depends_target_radial-test: phony || cmake_object_order_depends_target_demo cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build demos/CMakeFiles/radial-test.dir/radial-test.c.o: C_COMPILER__radial-test_unscanned_Debug /Users/<USER>/work/pixman2/demos/radial-test.c || cmake_object_order_depends_target_radial-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = demos/CMakeFiles/radial-test.dir/radial-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = demos/CMakeFiles/radial-test.dir
  OBJECT_FILE_DIR = demos/CMakeFiles/radial-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target radial-test


#############################################
# Link the executable demos/radial-test

build demos/radial-test: C_EXECUTABLE_LINKER__radial-test_Debug demos/CMakeFiles/radial-test.dir/radial-test.c.o | demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a pixman/libpixman-1.a /usr/local/lib/libpng.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = demos/libdemo.a  pixman/libpixman-1.a  test/utils/libtestutils.a  -lgtk-3  -lgdk-3  -lpangocairo-1.0  -lpango-1.0  -lharfbuzz  -lcairo-gobject  -lcairo  -lm  -ldl  -lfontconfig  -lfreetype  pixman/libpixman-1.a  -lgdk_pixbuf-2.0  -lpng16  -latk-1.0  -lgio-2.0  -lgobject-2.0  -lglib-2.0  -lintl  -lz  -framework CoreFoundation  -framework ApplicationServices  -lglib-2.0  -lintl  /usr/local/lib/libpng.dylib  -lz  -framework CoreFoundation  -framework ApplicationServices  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = demos/CMakeFiles/radial-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = demos/radial-test
  TARGET_PDB = radial-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target linear-gradient


#############################################
# Order-only phony target for linear-gradient

build cmake_object_order_depends_target_linear-gradient: phony || cmake_object_order_depends_target_demo cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build demos/CMakeFiles/linear-gradient.dir/linear-gradient.c.o: C_COMPILER__linear-gradient_unscanned_Debug /Users/<USER>/work/pixman2/demos/linear-gradient.c || cmake_object_order_depends_target_linear-gradient
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = demos/CMakeFiles/linear-gradient.dir/linear-gradient.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = demos/CMakeFiles/linear-gradient.dir
  OBJECT_FILE_DIR = demos/CMakeFiles/linear-gradient.dir


# =============================================================================
# Link build statements for EXECUTABLE target linear-gradient


#############################################
# Link the executable demos/linear-gradient

build demos/linear-gradient: C_EXECUTABLE_LINKER__linear-gradient_Debug demos/CMakeFiles/linear-gradient.dir/linear-gradient.c.o | demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a pixman/libpixman-1.a /usr/local/lib/libpng.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = demos/libdemo.a  pixman/libpixman-1.a  test/utils/libtestutils.a  -lgtk-3  -lgdk-3  -lpangocairo-1.0  -lpango-1.0  -lharfbuzz  -lcairo-gobject  -lcairo  -lm  -ldl  -lfontconfig  -lfreetype  pixman/libpixman-1.a  -lgdk_pixbuf-2.0  -lpng16  -latk-1.0  -lgio-2.0  -lgobject-2.0  -lglib-2.0  -lintl  -lz  -framework CoreFoundation  -framework ApplicationServices  -lglib-2.0  -lintl  /usr/local/lib/libpng.dylib  -lz  -framework CoreFoundation  -framework ApplicationServices  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = demos/CMakeFiles/linear-gradient.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = demos/linear-gradient
  TARGET_PDB = linear-gradient.dbg

# =============================================================================
# Object build statements for EXECUTABLE target conical-test


#############################################
# Order-only phony target for conical-test

build cmake_object_order_depends_target_conical-test: phony || cmake_object_order_depends_target_demo cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build demos/CMakeFiles/conical-test.dir/conical-test.c.o: C_COMPILER__conical-test_unscanned_Debug /Users/<USER>/work/pixman2/demos/conical-test.c || cmake_object_order_depends_target_conical-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = demos/CMakeFiles/conical-test.dir/conical-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = demos/CMakeFiles/conical-test.dir
  OBJECT_FILE_DIR = demos/CMakeFiles/conical-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target conical-test


#############################################
# Link the executable demos/conical-test

build demos/conical-test: C_EXECUTABLE_LINKER__conical-test_Debug demos/CMakeFiles/conical-test.dir/conical-test.c.o | demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a pixman/libpixman-1.a /usr/local/lib/libpng.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = demos/libdemo.a  pixman/libpixman-1.a  test/utils/libtestutils.a  -lgtk-3  -lgdk-3  -lpangocairo-1.0  -lpango-1.0  -lharfbuzz  -lcairo-gobject  -lcairo  -lm  -ldl  -lfontconfig  -lfreetype  pixman/libpixman-1.a  -lgdk_pixbuf-2.0  -lpng16  -latk-1.0  -lgio-2.0  -lgobject-2.0  -lglib-2.0  -lintl  -lz  -framework CoreFoundation  -framework ApplicationServices  -lglib-2.0  -lintl  /usr/local/lib/libpng.dylib  -lz  -framework CoreFoundation  -framework ApplicationServices  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = demos/CMakeFiles/conical-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = demos/conical-test
  TARGET_PDB = conical-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target tri-test


#############################################
# Order-only phony target for tri-test

build cmake_object_order_depends_target_tri-test: phony || cmake_object_order_depends_target_demo cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build demos/CMakeFiles/tri-test.dir/tri-test.c.o: C_COMPILER__tri-test_unscanned_Debug /Users/<USER>/work/pixman2/demos/tri-test.c || cmake_object_order_depends_target_tri-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = demos/CMakeFiles/tri-test.dir/tri-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = demos/CMakeFiles/tri-test.dir
  OBJECT_FILE_DIR = demos/CMakeFiles/tri-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target tri-test


#############################################
# Link the executable demos/tri-test

build demos/tri-test: C_EXECUTABLE_LINKER__tri-test_Debug demos/CMakeFiles/tri-test.dir/tri-test.c.o | demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a pixman/libpixman-1.a /usr/local/lib/libpng.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = demos/libdemo.a  pixman/libpixman-1.a  test/utils/libtestutils.a  -lgtk-3  -lgdk-3  -lpangocairo-1.0  -lpango-1.0  -lharfbuzz  -lcairo-gobject  -lcairo  -lm  -ldl  -lfontconfig  -lfreetype  pixman/libpixman-1.a  -lgdk_pixbuf-2.0  -lpng16  -latk-1.0  -lgio-2.0  -lgobject-2.0  -lglib-2.0  -lintl  -lz  -framework CoreFoundation  -framework ApplicationServices  -lglib-2.0  -lintl  /usr/local/lib/libpng.dylib  -lz  -framework CoreFoundation  -framework ApplicationServices  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = demos/CMakeFiles/tri-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = demos/tri-test
  TARGET_PDB = tri-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target checkerboard


#############################################
# Order-only phony target for checkerboard

build cmake_object_order_depends_target_checkerboard: phony || cmake_object_order_depends_target_demo cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build demos/CMakeFiles/checkerboard.dir/checkerboard.c.o: C_COMPILER__checkerboard_unscanned_Debug /Users/<USER>/work/pixman2/demos/checkerboard.c || cmake_object_order_depends_target_checkerboard
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = demos/CMakeFiles/checkerboard.dir/checkerboard.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = demos/CMakeFiles/checkerboard.dir
  OBJECT_FILE_DIR = demos/CMakeFiles/checkerboard.dir


# =============================================================================
# Link build statements for EXECUTABLE target checkerboard


#############################################
# Link the executable demos/checkerboard

build demos/checkerboard: C_EXECUTABLE_LINKER__checkerboard_Debug demos/CMakeFiles/checkerboard.dir/checkerboard.c.o | demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a pixman/libpixman-1.a /usr/local/lib/libpng.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = demos/libdemo.a  pixman/libpixman-1.a  test/utils/libtestutils.a  -lgtk-3  -lgdk-3  -lpangocairo-1.0  -lpango-1.0  -lharfbuzz  -lcairo-gobject  -lcairo  -lm  -ldl  -lfontconfig  -lfreetype  pixman/libpixman-1.a  -lgdk_pixbuf-2.0  -lpng16  -latk-1.0  -lgio-2.0  -lgobject-2.0  -lglib-2.0  -lintl  -lz  -framework CoreFoundation  -framework ApplicationServices  -lglib-2.0  -lintl  /usr/local/lib/libpng.dylib  -lz  -framework CoreFoundation  -framework ApplicationServices  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = demos/CMakeFiles/checkerboard.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = demos/checkerboard
  TARGET_PDB = checkerboard.dbg

# =============================================================================
# Object build statements for EXECUTABLE target srgb-test


#############################################
# Order-only phony target for srgb-test

build cmake_object_order_depends_target_srgb-test: phony || cmake_object_order_depends_target_demo cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build demos/CMakeFiles/srgb-test.dir/srgb-test.c.o: C_COMPILER__srgb-test_unscanned_Debug /Users/<USER>/work/pixman2/demos/srgb-test.c || cmake_object_order_depends_target_srgb-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = demos/CMakeFiles/srgb-test.dir/srgb-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = demos/CMakeFiles/srgb-test.dir
  OBJECT_FILE_DIR = demos/CMakeFiles/srgb-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target srgb-test


#############################################
# Link the executable demos/srgb-test

build demos/srgb-test: C_EXECUTABLE_LINKER__srgb-test_Debug demos/CMakeFiles/srgb-test.dir/srgb-test.c.o | demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a pixman/libpixman-1.a /usr/local/lib/libpng.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = demos/libdemo.a  pixman/libpixman-1.a  test/utils/libtestutils.a  -lgtk-3  -lgdk-3  -lpangocairo-1.0  -lpango-1.0  -lharfbuzz  -lcairo-gobject  -lcairo  -lm  -ldl  -lfontconfig  -lfreetype  pixman/libpixman-1.a  -lgdk_pixbuf-2.0  -lpng16  -latk-1.0  -lgio-2.0  -lgobject-2.0  -lglib-2.0  -lintl  -lz  -framework CoreFoundation  -framework ApplicationServices  -lglib-2.0  -lintl  /usr/local/lib/libpng.dylib  -lz  -framework CoreFoundation  -framework ApplicationServices  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = demos/CMakeFiles/srgb-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = demos/srgb-test
  TARGET_PDB = srgb-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target srgb-trap-test


#############################################
# Order-only phony target for srgb-trap-test

build cmake_object_order_depends_target_srgb-trap-test: phony || cmake_object_order_depends_target_demo cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build demos/CMakeFiles/srgb-trap-test.dir/srgb-trap-test.c.o: C_COMPILER__srgb-trap-test_unscanned_Debug /Users/<USER>/work/pixman2/demos/srgb-trap-test.c || cmake_object_order_depends_target_srgb-trap-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = demos/CMakeFiles/srgb-trap-test.dir/srgb-trap-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = demos/CMakeFiles/srgb-trap-test.dir
  OBJECT_FILE_DIR = demos/CMakeFiles/srgb-trap-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target srgb-trap-test


#############################################
# Link the executable demos/srgb-trap-test

build demos/srgb-trap-test: C_EXECUTABLE_LINKER__srgb-trap-test_Debug demos/CMakeFiles/srgb-trap-test.dir/srgb-trap-test.c.o | demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a pixman/libpixman-1.a /usr/local/lib/libpng.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = demos/libdemo.a  pixman/libpixman-1.a  test/utils/libtestutils.a  -lgtk-3  -lgdk-3  -lpangocairo-1.0  -lpango-1.0  -lharfbuzz  -lcairo-gobject  -lcairo  -lm  -ldl  -lfontconfig  -lfreetype  pixman/libpixman-1.a  -lgdk_pixbuf-2.0  -lpng16  -latk-1.0  -lgio-2.0  -lgobject-2.0  -lglib-2.0  -lintl  -lz  -framework CoreFoundation  -framework ApplicationServices  -lglib-2.0  -lintl  /usr/local/lib/libpng.dylib  -lz  -framework CoreFoundation  -framework ApplicationServices  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = demos/CMakeFiles/srgb-trap-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = demos/srgb-trap-test
  TARGET_PDB = srgb-trap-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target scale


#############################################
# Order-only phony target for scale

build cmake_object_order_depends_target_scale: phony || cmake_object_order_depends_target_demo cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build demos/CMakeFiles/scale.dir/scale.c.o: C_COMPILER__scale_unscanned_Debug /Users/<USER>/work/pixman2/demos/scale.c || cmake_object_order_depends_target_scale
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = demos/CMakeFiles/scale.dir/scale.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = demos/CMakeFiles/scale.dir
  OBJECT_FILE_DIR = demos/CMakeFiles/scale.dir


# =============================================================================
# Link build statements for EXECUTABLE target scale


#############################################
# Link the executable demos/scale

build demos/scale: C_EXECUTABLE_LINKER__scale_Debug demos/CMakeFiles/scale.dir/scale.c.o | demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a pixman/libpixman-1.a /usr/local/lib/libpng.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = demos/libdemo.a  pixman/libpixman-1.a  test/utils/libtestutils.a  -lgtk-3  -lgdk-3  -lpangocairo-1.0  -lpango-1.0  -lharfbuzz  -lcairo-gobject  -lcairo  -lm  -ldl  -lfontconfig  -lfreetype  pixman/libpixman-1.a  -lgdk_pixbuf-2.0  -lpng16  -latk-1.0  -lgio-2.0  -lgobject-2.0  -lglib-2.0  -lintl  -lz  -framework CoreFoundation  -framework ApplicationServices  -lglib-2.0  -lintl  /usr/local/lib/libpng.dylib  -lz  -framework CoreFoundation  -framework ApplicationServices  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = demos/CMakeFiles/scale.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = demos/scale
  TARGET_PDB = scale.dbg

# =============================================================================
# Object build statements for EXECUTABLE target dither


#############################################
# Order-only phony target for dither

build cmake_object_order_depends_target_dither: phony || cmake_object_order_depends_target_demo cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build demos/CMakeFiles/dither.dir/dither.c.o: C_COMPILER__dither_unscanned_Debug /Users/<USER>/work/pixman2/demos/dither.c || cmake_object_order_depends_target_dither
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = demos/CMakeFiles/dither.dir/dither.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/build/demos -I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0 -I/usr/local/Cellar/pango/1.56.3/include/pango-1.0 -I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz -I/usr/local/Cellar/graphite2/1.3.14/include -I/usr/local/include/cairo -I/usr/local/opt/freetype/include/freetype2 -I/usr/local/Cellar/pixman/0.46.0/include/pixman-1 -I/usr/local/include/gdk-pixbuf-2.0 -I/usr/local/opt/libpng/include/libpng16 -I/usr/local/opt/libtiff/include -I/usr/local/opt/zstd/include -I/usr/local/Cellar/xz/5.8.1/include -I/usr/local/opt/jpeg-turbo/include -I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0 -I/usr/local/Cellar/libepoxy/1.5.10/include -I/usr/local/Cellar/fribidi/1.0.16/include/fribidi -I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0 -I/usr/local/Cellar/glib/2.84.2/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi -I/usr/local/Cellar/glib/2.84.2/include/glib-2.0 -I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include -I/usr/local/opt/gettext/include -I/usr/local/Cellar/pcre2/10.45/include -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = demos/CMakeFiles/dither.dir
  OBJECT_FILE_DIR = demos/CMakeFiles/dither.dir


# =============================================================================
# Link build statements for EXECUTABLE target dither


#############################################
# Link the executable demos/dither

build demos/dither: C_EXECUTABLE_LINKER__dither_Debug demos/CMakeFiles/dither.dir/dither.c.o | demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a pixman/libpixman-1.a /usr/local/lib/libpng.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || demos/libdemo.a pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = demos/libdemo.a  pixman/libpixman-1.a  test/utils/libtestutils.a  -lgtk-3  -lgdk-3  -lpangocairo-1.0  -lpango-1.0  -lharfbuzz  -lcairo-gobject  -lcairo  -lm  -ldl  -lfontconfig  -lfreetype  pixman/libpixman-1.a  -lgdk_pixbuf-2.0  -lpng16  -latk-1.0  -lgio-2.0  -lgobject-2.0  -lglib-2.0  -lintl  -lz  -framework CoreFoundation  -framework ApplicationServices  -lglib-2.0  -lintl  /usr/local/lib/libpng.dylib  -lz  -framework CoreFoundation  -framework ApplicationServices  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = demos/CMakeFiles/dither.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = demos/dither
  TARGET_PDB = dither.dbg


#############################################
# Utility command for test

build demos/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/pixman2/build/demos && /usr/local/bin/ctest
  DESC = Running tests...
  pool = console
  restat = 1

build demos/test: phony demos/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build demos/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/pixman2/build/demos && /usr/local/bin/ccmake -S/Users/<USER>/work/pixman2 -B/Users/<USER>/work/pixman2/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build demos/edit_cache: phony demos/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build demos/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/pixman2/build/demos && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/pixman2 -B/Users/<USER>/work/pixman2/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build demos/rebuild_cache: phony demos/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build demos/list_install_components: phony


#############################################
# Utility command for install

build demos/CMakeFiles/install.util: CUSTOM_COMMAND demos/all
  COMMAND = cd /Users/<USER>/work/pixman2/build/demos && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build demos/install: phony demos/CMakeFiles/install.util


#############################################
# Utility command for install/local

build demos/CMakeFiles/install/local.util: CUSTOM_COMMAND demos/all
  COMMAND = cd /Users/<USER>/work/pixman2/build/demos && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build demos/install/local: phony demos/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build demos/CMakeFiles/install/strip.util: CUSTOM_COMMAND demos/all
  COMMAND = cd /Users/<USER>/work/pixman2/build/demos && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build demos/install/strip: phony demos/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/work/pixman2/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for EXECUTABLE target oob-test


#############################################
# Order-only phony target for oob-test

build cmake_object_order_depends_target_oob-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/oob-test.dir/oob-test.c.o: C_COMPILER__oob-test_unscanned_Debug /Users/<USER>/work/pixman2/test/oob-test.c || cmake_object_order_depends_target_oob-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/oob-test.dir/oob-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/oob-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/oob-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target oob-test


#############################################
# Link the executable test/oob-test

build test/oob-test: C_EXECUTABLE_LINKER__oob-test_Debug test/CMakeFiles/oob-test.dir/oob-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/oob-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/oob-test
  TARGET_PDB = oob-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target infinite-loop


#############################################
# Order-only phony target for infinite-loop

build cmake_object_order_depends_target_infinite-loop: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/infinite-loop.dir/infinite-loop.c.o: C_COMPILER__infinite-loop_unscanned_Debug /Users/<USER>/work/pixman2/test/infinite-loop.c || cmake_object_order_depends_target_infinite-loop
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/infinite-loop.dir/infinite-loop.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/infinite-loop.dir
  OBJECT_FILE_DIR = test/CMakeFiles/infinite-loop.dir


# =============================================================================
# Link build statements for EXECUTABLE target infinite-loop


#############################################
# Link the executable test/infinite-loop

build test/infinite-loop: C_EXECUTABLE_LINKER__infinite-loop_Debug test/CMakeFiles/infinite-loop.dir/infinite-loop.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/infinite-loop.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/infinite-loop
  TARGET_PDB = infinite-loop.dbg

# =============================================================================
# Object build statements for EXECUTABLE target trap-crasher


#############################################
# Order-only phony target for trap-crasher

build cmake_object_order_depends_target_trap-crasher: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/trap-crasher.dir/trap-crasher.c.o: C_COMPILER__trap-crasher_unscanned_Debug /Users/<USER>/work/pixman2/test/trap-crasher.c || cmake_object_order_depends_target_trap-crasher
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/trap-crasher.dir/trap-crasher.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/trap-crasher.dir
  OBJECT_FILE_DIR = test/CMakeFiles/trap-crasher.dir


# =============================================================================
# Link build statements for EXECUTABLE target trap-crasher


#############################################
# Link the executable test/trap-crasher

build test/trap-crasher: C_EXECUTABLE_LINKER__trap-crasher_Debug test/CMakeFiles/trap-crasher.dir/trap-crasher.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/trap-crasher.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/trap-crasher
  TARGET_PDB = trap-crasher.dbg

# =============================================================================
# Object build statements for EXECUTABLE target fence-image-self-test


#############################################
# Order-only phony target for fence-image-self-test

build cmake_object_order_depends_target_fence-image-self-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/fence-image-self-test.dir/fence-image-self-test.c.o: C_COMPILER__fence-image-self-test_unscanned_Debug /Users/<USER>/work/pixman2/test/fence-image-self-test.c || cmake_object_order_depends_target_fence-image-self-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/fence-image-self-test.dir/fence-image-self-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/fence-image-self-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/fence-image-self-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target fence-image-self-test


#############################################
# Link the executable test/fence-image-self-test

build test/fence-image-self-test: C_EXECUTABLE_LINKER__fence-image-self-test_Debug test/CMakeFiles/fence-image-self-test.dir/fence-image-self-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/fence-image-self-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/fence-image-self-test
  TARGET_PDB = fence-image-self-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target region-translate-test


#############################################
# Order-only phony target for region-translate-test

build cmake_object_order_depends_target_region-translate-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/region-translate-test.dir/region-translate-test.c.o: C_COMPILER__region-translate-test_unscanned_Debug /Users/<USER>/work/pixman2/test/region-translate-test.c || cmake_object_order_depends_target_region-translate-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/region-translate-test.dir/region-translate-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/region-translate-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/region-translate-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target region-translate-test


#############################################
# Link the executable test/region-translate-test

build test/region-translate-test: C_EXECUTABLE_LINKER__region-translate-test_Debug test/CMakeFiles/region-translate-test.dir/region-translate-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/region-translate-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/region-translate-test
  TARGET_PDB = region-translate-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target fetch-test


#############################################
# Order-only phony target for fetch-test

build cmake_object_order_depends_target_fetch-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/fetch-test.dir/fetch-test.c.o: C_COMPILER__fetch-test_unscanned_Debug /Users/<USER>/work/pixman2/test/fetch-test.c || cmake_object_order_depends_target_fetch-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/fetch-test.dir/fetch-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/fetch-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/fetch-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target fetch-test


#############################################
# Link the executable test/fetch-test

build test/fetch-test: C_EXECUTABLE_LINKER__fetch-test_Debug test/CMakeFiles/fetch-test.dir/fetch-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/fetch-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/fetch-test
  TARGET_PDB = fetch-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target a1-trap-test


#############################################
# Order-only phony target for a1-trap-test

build cmake_object_order_depends_target_a1-trap-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/a1-trap-test.dir/a1-trap-test.c.o: C_COMPILER__a1-trap-test_unscanned_Debug /Users/<USER>/work/pixman2/test/a1-trap-test.c || cmake_object_order_depends_target_a1-trap-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/a1-trap-test.dir/a1-trap-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/a1-trap-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/a1-trap-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target a1-trap-test


#############################################
# Link the executable test/a1-trap-test

build test/a1-trap-test: C_EXECUTABLE_LINKER__a1-trap-test_Debug test/CMakeFiles/a1-trap-test.dir/a1-trap-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/a1-trap-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/a1-trap-test
  TARGET_PDB = a1-trap-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target prng-test


#############################################
# Order-only phony target for prng-test

build cmake_object_order_depends_target_prng-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/prng-test.dir/prng-test.c.o: C_COMPILER__prng-test_unscanned_Debug /Users/<USER>/work/pixman2/test/prng-test.c || cmake_object_order_depends_target_prng-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/prng-test.dir/prng-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/prng-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/prng-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target prng-test


#############################################
# Link the executable test/prng-test

build test/prng-test: C_EXECUTABLE_LINKER__prng-test_Debug test/CMakeFiles/prng-test.dir/prng-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/prng-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/prng-test
  TARGET_PDB = prng-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target radial-invalid


#############################################
# Order-only phony target for radial-invalid

build cmake_object_order_depends_target_radial-invalid: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/radial-invalid.dir/radial-invalid.c.o: C_COMPILER__radial-invalid_unscanned_Debug /Users/<USER>/work/pixman2/test/radial-invalid.c || cmake_object_order_depends_target_radial-invalid
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/radial-invalid.dir/radial-invalid.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/radial-invalid.dir
  OBJECT_FILE_DIR = test/CMakeFiles/radial-invalid.dir


# =============================================================================
# Link build statements for EXECUTABLE target radial-invalid


#############################################
# Link the executable test/radial-invalid

build test/radial-invalid: C_EXECUTABLE_LINKER__radial-invalid_Debug test/CMakeFiles/radial-invalid.dir/radial-invalid.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/radial-invalid.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/radial-invalid
  TARGET_PDB = radial-invalid.dbg

# =============================================================================
# Object build statements for EXECUTABLE target pdf-op-test


#############################################
# Order-only phony target for pdf-op-test

build cmake_object_order_depends_target_pdf-op-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/pdf-op-test.dir/pdf-op-test.c.o: C_COMPILER__pdf-op-test_unscanned_Debug /Users/<USER>/work/pixman2/test/pdf-op-test.c || cmake_object_order_depends_target_pdf-op-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/pdf-op-test.dir/pdf-op-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/pdf-op-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/pdf-op-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target pdf-op-test


#############################################
# Link the executable test/pdf-op-test

build test/pdf-op-test: C_EXECUTABLE_LINKER__pdf-op-test_Debug test/CMakeFiles/pdf-op-test.dir/pdf-op-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/pdf-op-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/pdf-op-test
  TARGET_PDB = pdf-op-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target region-test


#############################################
# Order-only phony target for region-test

build cmake_object_order_depends_target_region-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/region-test.dir/region-test.c.o: C_COMPILER__region-test_unscanned_Debug /Users/<USER>/work/pixman2/test/region-test.c || cmake_object_order_depends_target_region-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/region-test.dir/region-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/region-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/region-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target region-test


#############################################
# Link the executable test/region-test

build test/region-test: C_EXECUTABLE_LINKER__region-test_Debug test/CMakeFiles/region-test.dir/region-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/region-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/region-test
  TARGET_PDB = region-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target region-fractional-test


#############################################
# Order-only phony target for region-fractional-test

build cmake_object_order_depends_target_region-fractional-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/region-fractional-test.dir/region-fractional-test.c.o: C_COMPILER__region-fractional-test_unscanned_Debug /Users/<USER>/work/pixman2/test/region-fractional-test.c || cmake_object_order_depends_target_region-fractional-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/region-fractional-test.dir/region-fractional-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/region-fractional-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/region-fractional-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target region-fractional-test


#############################################
# Link the executable test/region-fractional-test

build test/region-fractional-test: C_EXECUTABLE_LINKER__region-fractional-test_Debug test/CMakeFiles/region-fractional-test.dir/region-fractional-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/region-fractional-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/region-fractional-test
  TARGET_PDB = region-fractional-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target combiner-test


#############################################
# Order-only phony target for combiner-test

build cmake_object_order_depends_target_combiner-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/combiner-test.dir/combiner-test.c.o: C_COMPILER__combiner-test_unscanned_Debug /Users/<USER>/work/pixman2/test/combiner-test.c || cmake_object_order_depends_target_combiner-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/combiner-test.dir/combiner-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/combiner-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/combiner-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target combiner-test


#############################################
# Link the executable test/combiner-test

build test/combiner-test: C_EXECUTABLE_LINKER__combiner-test_Debug test/CMakeFiles/combiner-test.dir/combiner-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/combiner-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/combiner-test
  TARGET_PDB = combiner-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target scaling-crash-test


#############################################
# Order-only phony target for scaling-crash-test

build cmake_object_order_depends_target_scaling-crash-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/scaling-crash-test.dir/scaling-crash-test.c.o: C_COMPILER__scaling-crash-test_unscanned_Debug /Users/<USER>/work/pixman2/test/scaling-crash-test.c || cmake_object_order_depends_target_scaling-crash-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/scaling-crash-test.dir/scaling-crash-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/scaling-crash-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/scaling-crash-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target scaling-crash-test


#############################################
# Link the executable test/scaling-crash-test

build test/scaling-crash-test: C_EXECUTABLE_LINKER__scaling-crash-test_Debug test/CMakeFiles/scaling-crash-test.dir/scaling-crash-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/scaling-crash-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/scaling-crash-test
  TARGET_PDB = scaling-crash-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target alpha-loop


#############################################
# Order-only phony target for alpha-loop

build cmake_object_order_depends_target_alpha-loop: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/alpha-loop.dir/alpha-loop.c.o: C_COMPILER__alpha-loop_unscanned_Debug /Users/<USER>/work/pixman2/test/alpha-loop.c || cmake_object_order_depends_target_alpha-loop
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/alpha-loop.dir/alpha-loop.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/alpha-loop.dir
  OBJECT_FILE_DIR = test/CMakeFiles/alpha-loop.dir


# =============================================================================
# Link build statements for EXECUTABLE target alpha-loop


#############################################
# Link the executable test/alpha-loop

build test/alpha-loop: C_EXECUTABLE_LINKER__alpha-loop_Debug test/CMakeFiles/alpha-loop.dir/alpha-loop.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/alpha-loop.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/alpha-loop
  TARGET_PDB = alpha-loop.dbg

# =============================================================================
# Object build statements for EXECUTABLE target scaling-helpers-test


#############################################
# Order-only phony target for scaling-helpers-test

build cmake_object_order_depends_target_scaling-helpers-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/scaling-helpers-test.dir/scaling-helpers-test.c.o: C_COMPILER__scaling-helpers-test_unscanned_Debug /Users/<USER>/work/pixman2/test/scaling-helpers-test.c || cmake_object_order_depends_target_scaling-helpers-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/scaling-helpers-test.dir/scaling-helpers-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/scaling-helpers-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/scaling-helpers-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target scaling-helpers-test


#############################################
# Link the executable test/scaling-helpers-test

build test/scaling-helpers-test: C_EXECUTABLE_LINKER__scaling-helpers-test_Debug test/CMakeFiles/scaling-helpers-test.dir/scaling-helpers-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/scaling-helpers-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/scaling-helpers-test
  TARGET_PDB = scaling-helpers-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target rotate-test


#############################################
# Order-only phony target for rotate-test

build cmake_object_order_depends_target_rotate-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/rotate-test.dir/rotate-test.c.o: C_COMPILER__rotate-test_unscanned_Debug /Users/<USER>/work/pixman2/test/rotate-test.c || cmake_object_order_depends_target_rotate-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/rotate-test.dir/rotate-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/rotate-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/rotate-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target rotate-test


#############################################
# Link the executable test/rotate-test

build test/rotate-test: C_EXECUTABLE_LINKER__rotate-test_Debug test/CMakeFiles/rotate-test.dir/rotate-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/rotate-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/rotate-test
  TARGET_PDB = rotate-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target alphamap


#############################################
# Order-only phony target for alphamap

build cmake_object_order_depends_target_alphamap: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/alphamap.dir/alphamap.c.o: C_COMPILER__alphamap_unscanned_Debug /Users/<USER>/work/pixman2/test/alphamap.c || cmake_object_order_depends_target_alphamap
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/alphamap.dir/alphamap.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/alphamap.dir
  OBJECT_FILE_DIR = test/CMakeFiles/alphamap.dir


# =============================================================================
# Link build statements for EXECUTABLE target alphamap


#############################################
# Link the executable test/alphamap

build test/alphamap: C_EXECUTABLE_LINKER__alphamap_Debug test/CMakeFiles/alphamap.dir/alphamap.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/alphamap.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/alphamap
  TARGET_PDB = alphamap.dbg

# =============================================================================
# Object build statements for EXECUTABLE target gradient-crash-test


#############################################
# Order-only phony target for gradient-crash-test

build cmake_object_order_depends_target_gradient-crash-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/gradient-crash-test.dir/gradient-crash-test.c.o: C_COMPILER__gradient-crash-test_unscanned_Debug /Users/<USER>/work/pixman2/test/gradient-crash-test.c || cmake_object_order_depends_target_gradient-crash-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/gradient-crash-test.dir/gradient-crash-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/gradient-crash-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/gradient-crash-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target gradient-crash-test


#############################################
# Link the executable test/gradient-crash-test

build test/gradient-crash-test: C_EXECUTABLE_LINKER__gradient-crash-test_Debug test/CMakeFiles/gradient-crash-test.dir/gradient-crash-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/gradient-crash-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/gradient-crash-test
  TARGET_PDB = gradient-crash-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target pixel-test


#############################################
# Order-only phony target for pixel-test

build cmake_object_order_depends_target_pixel-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/pixel-test.dir/pixel-test.c.o: C_COMPILER__pixel-test_unscanned_Debug /Users/<USER>/work/pixman2/test/pixel-test.c || cmake_object_order_depends_target_pixel-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/pixel-test.dir/pixel-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/pixel-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/pixel-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target pixel-test


#############################################
# Link the executable test/pixel-test

build test/pixel-test: C_EXECUTABLE_LINKER__pixel-test_Debug test/CMakeFiles/pixel-test.dir/pixel-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/pixel-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/pixel-test
  TARGET_PDB = pixel-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target matrix-test


#############################################
# Order-only phony target for matrix-test

build cmake_object_order_depends_target_matrix-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/matrix-test.dir/matrix-test.c.o: C_COMPILER__matrix-test_unscanned_Debug /Users/<USER>/work/pixman2/test/matrix-test.c || cmake_object_order_depends_target_matrix-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/matrix-test.dir/matrix-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/matrix-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/matrix-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target matrix-test


#############################################
# Link the executable test/matrix-test

build test/matrix-test: C_EXECUTABLE_LINKER__matrix-test_Debug test/CMakeFiles/matrix-test.dir/matrix-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/matrix-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/matrix-test
  TARGET_PDB = matrix-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target filter-reduction-test


#############################################
# Order-only phony target for filter-reduction-test

build cmake_object_order_depends_target_filter-reduction-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/filter-reduction-test.dir/filter-reduction-test.c.o: C_COMPILER__filter-reduction-test_unscanned_Debug /Users/<USER>/work/pixman2/test/filter-reduction-test.c || cmake_object_order_depends_target_filter-reduction-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/filter-reduction-test.dir/filter-reduction-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/filter-reduction-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/filter-reduction-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target filter-reduction-test


#############################################
# Link the executable test/filter-reduction-test

build test/filter-reduction-test: C_EXECUTABLE_LINKER__filter-reduction-test_Debug test/CMakeFiles/filter-reduction-test.dir/filter-reduction-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/filter-reduction-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/filter-reduction-test
  TARGET_PDB = filter-reduction-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target composite-traps-test


#############################################
# Order-only phony target for composite-traps-test

build cmake_object_order_depends_target_composite-traps-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/composite-traps-test.dir/composite-traps-test.c.o: C_COMPILER__composite-traps-test_unscanned_Debug /Users/<USER>/work/pixman2/test/composite-traps-test.c || cmake_object_order_depends_target_composite-traps-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/composite-traps-test.dir/composite-traps-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/composite-traps-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/composite-traps-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target composite-traps-test


#############################################
# Link the executable test/composite-traps-test

build test/composite-traps-test: C_EXECUTABLE_LINKER__composite-traps-test_Debug test/CMakeFiles/composite-traps-test.dir/composite-traps-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/composite-traps-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/composite-traps-test
  TARGET_PDB = composite-traps-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target region-contains-test


#############################################
# Order-only phony target for region-contains-test

build cmake_object_order_depends_target_region-contains-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/region-contains-test.dir/region-contains-test.c.o: C_COMPILER__region-contains-test_unscanned_Debug /Users/<USER>/work/pixman2/test/region-contains-test.c || cmake_object_order_depends_target_region-contains-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/region-contains-test.dir/region-contains-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/region-contains-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/region-contains-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target region-contains-test


#############################################
# Link the executable test/region-contains-test

build test/region-contains-test: C_EXECUTABLE_LINKER__region-contains-test_Debug test/CMakeFiles/region-contains-test.dir/region-contains-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/region-contains-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/region-contains-test
  TARGET_PDB = region-contains-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target glyph-test


#############################################
# Order-only phony target for glyph-test

build cmake_object_order_depends_target_glyph-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/glyph-test.dir/glyph-test.c.o: C_COMPILER__glyph-test_unscanned_Debug /Users/<USER>/work/pixman2/test/glyph-test.c || cmake_object_order_depends_target_glyph-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/glyph-test.dir/glyph-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/glyph-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/glyph-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target glyph-test


#############################################
# Link the executable test/glyph-test

build test/glyph-test: C_EXECUTABLE_LINKER__glyph-test_Debug test/CMakeFiles/glyph-test.dir/glyph-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/glyph-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/glyph-test
  TARGET_PDB = glyph-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target solid-test


#############################################
# Order-only phony target for solid-test

build cmake_object_order_depends_target_solid-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/solid-test.dir/solid-test.c.o: C_COMPILER__solid-test_unscanned_Debug /Users/<USER>/work/pixman2/test/solid-test.c || cmake_object_order_depends_target_solid-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/solid-test.dir/solid-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/solid-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/solid-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target solid-test


#############################################
# Link the executable test/solid-test

build test/solid-test: C_EXECUTABLE_LINKER__solid-test_Debug test/CMakeFiles/solid-test.dir/solid-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/solid-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/solid-test
  TARGET_PDB = solid-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target stress-test


#############################################
# Order-only phony target for stress-test

build cmake_object_order_depends_target_stress-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/stress-test.dir/stress-test.c.o: C_COMPILER__stress-test_unscanned_Debug /Users/<USER>/work/pixman2/test/stress-test.c || cmake_object_order_depends_target_stress-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/stress-test.dir/stress-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/stress-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/stress-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target stress-test


#############################################
# Link the executable test/stress-test

build test/stress-test: C_EXECUTABLE_LINKER__stress-test_Debug test/CMakeFiles/stress-test.dir/stress-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/stress-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/stress-test
  TARGET_PDB = stress-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target cover-test


#############################################
# Order-only phony target for cover-test

build cmake_object_order_depends_target_cover-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/cover-test.dir/cover-test.c.o: C_COMPILER__cover-test_unscanned_Debug /Users/<USER>/work/pixman2/test/cover-test.c || cmake_object_order_depends_target_cover-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/cover-test.dir/cover-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/cover-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/cover-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target cover-test


#############################################
# Link the executable test/cover-test

build test/cover-test: C_EXECUTABLE_LINKER__cover-test_Debug test/CMakeFiles/cover-test.dir/cover-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/cover-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/cover-test
  TARGET_PDB = cover-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target blitters-test


#############################################
# Order-only phony target for blitters-test

build cmake_object_order_depends_target_blitters-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/blitters-test.dir/blitters-test.c.o: C_COMPILER__blitters-test_unscanned_Debug /Users/<USER>/work/pixman2/test/blitters-test.c || cmake_object_order_depends_target_blitters-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/blitters-test.dir/blitters-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/blitters-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/blitters-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target blitters-test


#############################################
# Link the executable test/blitters-test

build test/blitters-test: C_EXECUTABLE_LINKER__blitters-test_Debug test/CMakeFiles/blitters-test.dir/blitters-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/blitters-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/blitters-test
  TARGET_PDB = blitters-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target affine-test


#############################################
# Order-only phony target for affine-test

build cmake_object_order_depends_target_affine-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/affine-test.dir/affine-test.c.o: C_COMPILER__affine-test_unscanned_Debug /Users/<USER>/work/pixman2/test/affine-test.c || cmake_object_order_depends_target_affine-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/affine-test.dir/affine-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/affine-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/affine-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target affine-test


#############################################
# Link the executable test/affine-test

build test/affine-test: C_EXECUTABLE_LINKER__affine-test_Debug test/CMakeFiles/affine-test.dir/affine-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/affine-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/affine-test
  TARGET_PDB = affine-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target scaling-test


#############################################
# Order-only phony target for scaling-test

build cmake_object_order_depends_target_scaling-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/scaling-test.dir/scaling-test.c.o: C_COMPILER__scaling-test_unscanned_Debug /Users/<USER>/work/pixman2/test/scaling-test.c || cmake_object_order_depends_target_scaling-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/scaling-test.dir/scaling-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/scaling-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/scaling-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target scaling-test


#############################################
# Link the executable test/scaling-test

build test/scaling-test: C_EXECUTABLE_LINKER__scaling-test_Debug test/CMakeFiles/scaling-test.dir/scaling-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/scaling-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/scaling-test
  TARGET_PDB = scaling-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target composite


#############################################
# Order-only phony target for composite

build cmake_object_order_depends_target_composite: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/composite.dir/composite.c.o: C_COMPILER__composite_unscanned_Debug /Users/<USER>/work/pixman2/test/composite.c || cmake_object_order_depends_target_composite
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/composite.dir/composite.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/composite.dir
  OBJECT_FILE_DIR = test/CMakeFiles/composite.dir


# =============================================================================
# Link build statements for EXECUTABLE target composite


#############################################
# Link the executable test/composite

build test/composite: C_EXECUTABLE_LINKER__composite_Debug test/CMakeFiles/composite.dir/composite.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/composite.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/composite
  TARGET_PDB = composite.dbg

# =============================================================================
# Object build statements for EXECUTABLE target tolerance-test


#############################################
# Order-only phony target for tolerance-test

build cmake_object_order_depends_target_tolerance-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/tolerance-test.dir/tolerance-test.c.o: C_COMPILER__tolerance-test_unscanned_Debug /Users/<USER>/work/pixman2/test/tolerance-test.c || cmake_object_order_depends_target_tolerance-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/tolerance-test.dir/tolerance-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/tolerance-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/tolerance-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target tolerance-test


#############################################
# Link the executable test/tolerance-test

build test/tolerance-test: C_EXECUTABLE_LINKER__tolerance-test_Debug test/CMakeFiles/tolerance-test.dir/tolerance-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/tolerance-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/tolerance-test
  TARGET_PDB = tolerance-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target neg-stride-test


#############################################
# Order-only phony target for neg-stride-test

build cmake_object_order_depends_target_neg-stride-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/neg-stride-test.dir/neg-stride-test.c.o: C_COMPILER__neg-stride-test_unscanned_Debug /Users/<USER>/work/pixman2/test/neg-stride-test.c || cmake_object_order_depends_target_neg-stride-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/neg-stride-test.dir/neg-stride-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/neg-stride-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/neg-stride-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target neg-stride-test


#############################################
# Link the executable test/neg-stride-test

build test/neg-stride-test: C_EXECUTABLE_LINKER__neg-stride-test_Debug test/CMakeFiles/neg-stride-test.dir/neg-stride-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/neg-stride-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/neg-stride-test
  TARGET_PDB = neg-stride-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target thread-test


#############################################
# Order-only phony target for thread-test

build cmake_object_order_depends_target_thread-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/thread-test.dir/thread-test.c.o: C_COMPILER__thread-test_unscanned_Debug /Users/<USER>/work/pixman2/test/thread-test.c || cmake_object_order_depends_target_thread-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/thread-test.dir/thread-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/thread-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/thread-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target thread-test


#############################################
# Link the executable test/thread-test

build test/thread-test: C_EXECUTABLE_LINKER__thread-test_Debug test/CMakeFiles/thread-test.dir/thread-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a /usr/local/lib/libpng.dylib pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  /usr/local/lib/libpng.dylib  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd
  OBJECT_DIR = test/CMakeFiles/thread-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/thread-test
  TARGET_PDB = thread-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target lowlevel-blt-bench


#############################################
# Order-only phony target for lowlevel-blt-bench

build cmake_object_order_depends_target_lowlevel-blt-bench: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/lowlevel-blt-bench.dir/lowlevel-blt-bench.c.o: C_COMPILER__lowlevel-blt-bench_unscanned_Debug /Users/<USER>/work/pixman2/test/lowlevel-blt-bench.c || cmake_object_order_depends_target_lowlevel-blt-bench
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/lowlevel-blt-bench.dir/lowlevel-blt-bench.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/lowlevel-blt-bench.dir
  OBJECT_FILE_DIR = test/CMakeFiles/lowlevel-blt-bench.dir


# =============================================================================
# Link build statements for EXECUTABLE target lowlevel-blt-bench


#############################################
# Link the executable test/lowlevel-blt-bench

build test/lowlevel-blt-bench: C_EXECUTABLE_LINKER__lowlevel-blt-bench_Debug test/CMakeFiles/lowlevel-blt-bench.dir/lowlevel-blt-bench.c.o | pixman/libpixman-1.a test/utils/libtestutils.a pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd /usr/local/lib/libpng.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd  /usr/local/lib/libpng.dylib  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd
  OBJECT_DIR = test/CMakeFiles/lowlevel-blt-bench.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/lowlevel-blt-bench
  TARGET_PDB = lowlevel-blt-bench.dbg

# =============================================================================
# Object build statements for EXECUTABLE target radial-perf-test


#############################################
# Order-only phony target for radial-perf-test

build cmake_object_order_depends_target_radial-perf-test: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/radial-perf-test.dir/radial-perf-test.c.o: C_COMPILER__radial-perf-test_unscanned_Debug /Users/<USER>/work/pixman2/test/radial-perf-test.c || cmake_object_order_depends_target_radial-perf-test
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/radial-perf-test.dir/radial-perf-test.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/radial-perf-test.dir
  OBJECT_FILE_DIR = test/CMakeFiles/radial-perf-test.dir


# =============================================================================
# Link build statements for EXECUTABLE target radial-perf-test


#############################################
# Link the executable test/radial-perf-test

build test/radial-perf-test: C_EXECUTABLE_LINKER__radial-perf-test_Debug test/CMakeFiles/radial-perf-test.dir/radial-perf-test.c.o | pixman/libpixman-1.a test/utils/libtestutils.a pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd /usr/local/lib/libpng.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd  /usr/local/lib/libpng.dylib  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd
  OBJECT_DIR = test/CMakeFiles/radial-perf-test.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/radial-perf-test
  TARGET_PDB = radial-perf-test.dbg

# =============================================================================
# Object build statements for EXECUTABLE target check-formats


#############################################
# Order-only phony target for check-formats

build cmake_object_order_depends_target_check-formats: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/check-formats.dir/check-formats.c.o: C_COMPILER__check-formats_unscanned_Debug /Users/<USER>/work/pixman2/test/check-formats.c || cmake_object_order_depends_target_check-formats
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/check-formats.dir/check-formats.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/check-formats.dir
  OBJECT_FILE_DIR = test/CMakeFiles/check-formats.dir


# =============================================================================
# Link build statements for EXECUTABLE target check-formats


#############################################
# Link the executable test/check-formats

build test/check-formats: C_EXECUTABLE_LINKER__check-formats_Debug test/CMakeFiles/check-formats.dir/check-formats.c.o | pixman/libpixman-1.a test/utils/libtestutils.a pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd /usr/local/lib/libpng.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd  /usr/local/lib/libpng.dylib  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd
  OBJECT_DIR = test/CMakeFiles/check-formats.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/check-formats
  TARGET_PDB = check-formats.dbg

# =============================================================================
# Object build statements for EXECUTABLE target scaling-bench


#############################################
# Order-only phony target for scaling-bench

build cmake_object_order_depends_target_scaling-bench: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/scaling-bench.dir/scaling-bench.c.o: C_COMPILER__scaling-bench_unscanned_Debug /Users/<USER>/work/pixman2/test/scaling-bench.c || cmake_object_order_depends_target_scaling-bench
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/scaling-bench.dir/scaling-bench.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/scaling-bench.dir
  OBJECT_FILE_DIR = test/CMakeFiles/scaling-bench.dir


# =============================================================================
# Link build statements for EXECUTABLE target scaling-bench


#############################################
# Link the executable test/scaling-bench

build test/scaling-bench: C_EXECUTABLE_LINKER__scaling-bench_Debug test/CMakeFiles/scaling-bench.dir/scaling-bench.c.o | pixman/libpixman-1.a test/utils/libtestutils.a pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd /usr/local/lib/libpng.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd  /usr/local/lib/libpng.dylib  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd
  OBJECT_DIR = test/CMakeFiles/scaling-bench.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/scaling-bench
  TARGET_PDB = scaling-bench.dbg

# =============================================================================
# Object build statements for EXECUTABLE target affine-bench


#############################################
# Order-only phony target for affine-bench

build cmake_object_order_depends_target_affine-bench: phony || cmake_object_order_depends_target_pixman-1 cmake_object_order_depends_target_testutils

build test/CMakeFiles/affine-bench.dir/affine-bench.c.o: C_COMPILER__affine-bench_unscanned_Debug /Users/<USER>/work/pixman2/test/affine-bench.c || cmake_object_order_depends_target_affine-bench
  CONFIG = Debug
  DEFINES = -DHAVE_CONFIG_H
  DEP_FILE = test/CMakeFiles/affine-bench.dir/affine-bench.c.o.d
  FLAGS = -g -std=gnu99
  INCLUDES = -I/Users/<USER>/work/pixman2/build -I/Users/<USER>/work/pixman2/pixman -I/Users/<USER>/work/pixman2/build/pixman -I/Users/<USER>/work/pixman2/test/utils
  OBJECT_DIR = test/CMakeFiles/affine-bench.dir
  OBJECT_FILE_DIR = test/CMakeFiles/affine-bench.dir


# =============================================================================
# Link build statements for EXECUTABLE target affine-bench


#############################################
# Link the executable test/affine-bench

build test/affine-bench: C_EXECUTABLE_LINKER__affine-bench_Debug test/CMakeFiles/affine-bench.dir/affine-bench.c.o | pixman/libpixman-1.a test/utils/libtestutils.a pixman/libpixman-1.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd /usr/local/lib/libpng.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd || pixman/libpixman-1.a test/utils/libtestutils.a
  CONFIG = Debug
  FLAGS = -g
  LINK_LIBRARIES = pixman/libpixman-1.a  test/utils/libtestutils.a  pixman/libpixman-1.a  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd  /usr/local/lib/libpng.dylib  /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd
  OBJECT_DIR = test/CMakeFiles/affine-bench.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = test/affine-bench
  TARGET_PDB = affine-bench.dbg


#############################################
# Utility command for test

build test/CMakeFiles/test.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/pixman2/build/test && /usr/local/bin/ctest
  DESC = Running tests...
  pool = console
  restat = 1

build test/test: phony test/CMakeFiles/test.util


#############################################
# Utility command for edit_cache

build test/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/pixman2/build/test && /usr/local/bin/ccmake -S/Users/<USER>/work/pixman2 -B/Users/<USER>/work/pixman2/build
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build test/edit_cache: phony test/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build test/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/work/pixman2/build/test && /usr/local/bin/cmake --regenerate-during-build -S/Users/<USER>/work/pixman2 -B/Users/<USER>/work/pixman2/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build test/rebuild_cache: phony test/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build test/list_install_components: phony


#############################################
# Utility command for install

build test/CMakeFiles/install.util: CUSTOM_COMMAND test/all
  COMMAND = cd /Users/<USER>/work/pixman2/build/test && /usr/local/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build test/install: phony test/CMakeFiles/install.util


#############################################
# Utility command for install/local

build test/CMakeFiles/install/local.util: CUSTOM_COMMAND test/all
  COMMAND = cd /Users/<USER>/work/pixman2/build/test && /usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build test/install/local: phony test/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build test/CMakeFiles/install/strip.util: CUSTOM_COMMAND test/all
  COMMAND = cd /Users/<USER>/work/pixman2/build/test && /usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build test/install/strip: phony test/CMakeFiles/install/strip.util

# =============================================================================
# Target aliases.

build a1-trap-test: phony test/a1-trap-test

build affine-bench: phony test/affine-bench

build affine-test: phony test/affine-test

build alpha-loop: phony test/alpha-loop

build alpha-test: phony demos/alpha-test

build alphamap: phony test/alphamap

build blitters-test: phony test/blitters-test

build check-formats: phony test/check-formats

build checkerboard: phony demos/checkerboard

build clip-test: phony demos/clip-test

build combiner-test: phony test/combiner-test

build composite: phony test/composite

build composite-test: phony demos/composite-test

build composite-traps-test: phony test/composite-traps-test

build conical-test: phony demos/conical-test

build convolution-test: phony demos/convolution-test

build cover-test: phony test/cover-test

build demo: phony demos/libdemo.a

build dither: phony demos/dither

build fence-image-self-test: phony test/fence-image-self-test

build fetch-test: phony test/fetch-test

build filter-reduction-test: phony test/filter-reduction-test

build glyph-test: phony test/glyph-test

build gradient-crash-test: phony test/gradient-crash-test

build gradient-test: phony demos/gradient-test

build infinite-loop: phony test/infinite-loop

build libdemo.a: phony demos/libdemo.a

build libpixman-1.a: phony pixman/libpixman-1.a

build libtestutils.a: phony test/utils/libtestutils.a

build linear-gradient: phony demos/linear-gradient

build lowlevel-blt-bench: phony test/lowlevel-blt-bench

build matrix-test: phony test/matrix-test

build neg-stride-test: phony test/neg-stride-test

build oob-test: phony test/oob-test

build pdf-op-test: phony test/pdf-op-test

build pixel-test: phony test/pixel-test

build pixman-1: phony pixman/libpixman-1.a

build prng-test: phony test/prng-test

build radial-invalid: phony test/radial-invalid

build radial-perf-test: phony test/radial-perf-test

build radial-test: phony demos/radial-test

build region-contains-test: phony test/region-contains-test

build region-fractional-test: phony test/region-fractional-test

build region-test: phony test/region-test

build region-translate-test: phony test/region-translate-test

build rotate-test: phony test/rotate-test

build scale: phony demos/scale

build scaling-bench: phony test/scaling-bench

build scaling-crash-test: phony test/scaling-crash-test

build scaling-helpers-test: phony test/scaling-helpers-test

build scaling-test: phony test/scaling-test

build screen-test: phony demos/screen-test

build solid-test: phony test/solid-test

build srgb-test: phony demos/srgb-test

build srgb-trap-test: phony demos/srgb-trap-test

build stress-test: phony test/stress-test

build testutils: phony test/utils/libtestutils.a

build thread-test: phony test/thread-test

build tolerance-test: phony test/tolerance-test

build trap-crasher: phony test/trap-crasher

build trap-test: phony demos/trap-test

build tri-test: phony demos/tri-test

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /Users/<USER>/work/pixman2/build

build all: phony pixman/all test/utils/all demos/all test/all

# =============================================================================

#############################################
# Folder: /Users/<USER>/work/pixman2/build/demos

build demos/all: phony demos/libdemo.a demos/gradient-test demos/alpha-test demos/composite-test demos/clip-test demos/trap-test demos/screen-test demos/convolution-test demos/radial-test demos/linear-gradient demos/conical-test demos/tri-test demos/checkerboard demos/srgb-test demos/srgb-trap-test demos/scale demos/dither

# =============================================================================

#############################################
# Folder: /Users/<USER>/work/pixman2/build/pixman

build pixman/all: phony pixman/libpixman-1.a

# =============================================================================

#############################################
# Folder: /Users/<USER>/work/pixman2/build/test

build test/all: phony test/oob-test test/infinite-loop test/trap-crasher test/fence-image-self-test test/region-translate-test test/fetch-test test/a1-trap-test test/prng-test test/radial-invalid test/pdf-op-test test/region-test test/region-fractional-test test/combiner-test test/scaling-crash-test test/alpha-loop test/scaling-helpers-test test/rotate-test test/alphamap test/gradient-crash-test test/pixel-test test/matrix-test test/filter-reduction-test test/composite-traps-test test/region-contains-test test/glyph-test test/solid-test test/stress-test test/cover-test test/blitters-test test/affine-test test/scaling-test test/composite test/tolerance-test test/neg-stride-test test/thread-test test/lowlevel-blt-bench test/radial-perf-test test/check-formats test/scaling-bench test/affine-bench

# =============================================================================

#############################################
# Folder: /Users/<USER>/work/pixman2/build/test/utils

build test/utils/all: phony test/utils/libtestutils.a

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja /Users/<USER>/work/pixman2/build/cmake_install.cmake /Users/<USER>/work/pixman2/build/pixman/cmake_install.cmake /Users/<USER>/work/pixman2/build/test/utils/cmake_install.cmake /Users/<USER>/work/pixman2/build/demos/cmake_install.cmake /Users/<USER>/work/pixman2/build/test/cmake_install.cmake /Users/<USER>/work/pixman2/build/CTestTestfile.cmake /Users/<USER>/work/pixman2/build/test/CTestTestfile.cmake: RERUN_CMAKE | /Users/<USER>/work/pixman2/CMakeLists.txt /Users/<USER>/work/pixman2/cmake/PixmanSIMD.cmake /Users/<USER>/work/pixman2/cmake/config.h.in /Users/<USER>/work/pixman2/demos/CMakeLists.txt /Users/<USER>/work/pixman2/pixman-1.pc.in /Users/<USER>/work/pixman2/pixman/CMakeLists.txt /Users/<USER>/work/pixman2/pixman/pixman-version.h.in /Users/<USER>/work/pixman2/test/CMakeLists.txt /Users/<USER>/work/pixman2/test/utils/CMakeLists.txt /usr/local/share/cmake/Modules/CMakeCInformation.cmake /usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake /usr/local/share/cmake/Modules/CMakeGenericSystem.cmake /usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake /usr/local/share/cmake/Modules/CMakeLanguageInformation.cmake /usr/local/share/cmake/Modules/CMakeParseImplicitLinkInfo.cmake /usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /usr/local/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake /usr/local/share/cmake/Modules/CheckCSourceRuns.cmake /usr/local/share/cmake/Modules/CheckFunctionExists.cmake /usr/local/share/cmake/Modules/CheckIncludeFile.cmake /usr/local/share/cmake/Modules/CheckIncludeFileCXX.cmake /usr/local/share/cmake/Modules/CheckLibraryExists.cmake /usr/local/share/cmake/Modules/CheckSymbolExists.cmake /usr/local/share/cmake/Modules/CheckTypeSize.cmake /usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake /usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/local/share/cmake/Modules/Compiler/Clang.cmake /usr/local/share/cmake/Modules/Compiler/GNU.cmake /usr/local/share/cmake/Modules/FindOpenMP.cmake /usr/local/share/cmake/Modules/FindPNG.cmake /usr/local/share/cmake/Modules/FindPackageHandleStandardArgs.cmake /usr/local/share/cmake/Modules/FindPackageMessage.cmake /usr/local/share/cmake/Modules/FindPkgConfig.cmake /usr/local/share/cmake/Modules/FindThreads.cmake /usr/local/share/cmake/Modules/FindZLIB.cmake /usr/local/share/cmake/Modules/GNUInstallDirs.cmake /usr/local/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake /usr/local/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake /usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake /usr/local/share/cmake/Modules/Internal/CheckSourceRuns.cmake /usr/local/share/cmake/Modules/Linker/AppleClang-C.cmake /usr/local/share/cmake/Modules/Linker/AppleClang.cmake /usr/local/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake /usr/local/share/cmake/Modules/Platform/Apple-Clang-C.cmake /usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake /usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake /usr/local/share/cmake/Modules/Platform/Darwin.cmake /usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake /usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake /usr/local/share/cmake/Modules/Platform/UnixPaths.cmake /usr/local/share/cmake/Modules/SelectLibraryConfigurations.cmake /usr/local/share/cmake/Modules/TestBigEndian.cmake CMakeCache.txt CMakeFiles/4.0.2/CMakeCCompiler.cmake CMakeFiles/4.0.2/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /Users/<USER>/work/pixman2/CMakeLists.txt /Users/<USER>/work/pixman2/cmake/PixmanSIMD.cmake /Users/<USER>/work/pixman2/cmake/config.h.in /Users/<USER>/work/pixman2/demos/CMakeLists.txt /Users/<USER>/work/pixman2/pixman-1.pc.in /Users/<USER>/work/pixman2/pixman/CMakeLists.txt /Users/<USER>/work/pixman2/pixman/pixman-version.h.in /Users/<USER>/work/pixman2/test/CMakeLists.txt /Users/<USER>/work/pixman2/test/utils/CMakeLists.txt /usr/local/share/cmake/Modules/CMakeCInformation.cmake /usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake /usr/local/share/cmake/Modules/CMakeGenericSystem.cmake /usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake /usr/local/share/cmake/Modules/CMakeLanguageInformation.cmake /usr/local/share/cmake/Modules/CMakeParseImplicitLinkInfo.cmake /usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /usr/local/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake /usr/local/share/cmake/Modules/CheckCSourceRuns.cmake /usr/local/share/cmake/Modules/CheckFunctionExists.cmake /usr/local/share/cmake/Modules/CheckIncludeFile.cmake /usr/local/share/cmake/Modules/CheckIncludeFileCXX.cmake /usr/local/share/cmake/Modules/CheckLibraryExists.cmake /usr/local/share/cmake/Modules/CheckSymbolExists.cmake /usr/local/share/cmake/Modules/CheckTypeSize.cmake /usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake /usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/local/share/cmake/Modules/Compiler/Clang.cmake /usr/local/share/cmake/Modules/Compiler/GNU.cmake /usr/local/share/cmake/Modules/FindOpenMP.cmake /usr/local/share/cmake/Modules/FindPNG.cmake /usr/local/share/cmake/Modules/FindPackageHandleStandardArgs.cmake /usr/local/share/cmake/Modules/FindPackageMessage.cmake /usr/local/share/cmake/Modules/FindPkgConfig.cmake /usr/local/share/cmake/Modules/FindThreads.cmake /usr/local/share/cmake/Modules/FindZLIB.cmake /usr/local/share/cmake/Modules/GNUInstallDirs.cmake /usr/local/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake /usr/local/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake /usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake /usr/local/share/cmake/Modules/Internal/CheckSourceRuns.cmake /usr/local/share/cmake/Modules/Linker/AppleClang-C.cmake /usr/local/share/cmake/Modules/Linker/AppleClang.cmake /usr/local/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake /usr/local/share/cmake/Modules/Platform/Apple-Clang-C.cmake /usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake /usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake /usr/local/share/cmake/Modules/Platform/Darwin.cmake /usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake /usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake /usr/local/share/cmake/Modules/Platform/UnixPaths.cmake /usr/local/share/cmake/Modules/SelectLibraryConfigurations.cmake /usr/local/share/cmake/Modules/TestBigEndian.cmake CMakeCache.txt CMakeFiles/4.0.2/CMakeCCompiler.cmake CMakeFiles/4.0.2/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
