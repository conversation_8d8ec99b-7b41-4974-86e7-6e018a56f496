#ifndef PIXMAN_CONFIG_H
#define PIXMAN_CONFIG_H

/* Define to 1 if you have the <fenv.h> header file. */
#define HAVE_FENV_H 1

/* Define to 1 if you have the <sys/mman.h> header file. */
#define HAVE_SYS_MMAN_H 1

/* Define to 1 if you have the <unistd.h> header file. */
#define HAVE_UNISTD_H 1

/* Define to 1 if you have the `alarm' function. */
#define HAVE_ALARM 1

/* Define to 1 if you have the `mmap' function. */
#define HAVE_MMAP 1

/* Define to 1 if you have the `mprotect' function. */
#define HAVE_MPROTECT 1

/* Define to 1 if you have the `getpagesize' function. */
#define HAVE_GETPAGESIZE 1

/* Define to 1 if you have the `getisax' function. */
/* #undef HAVE_GETISAX */

/* Define to 1 if you have POSIX threads libraries and header files. */
#define HAVE_PTHREADS 1

/* Define to 1 if you have the `sigaction' function. */
#define HAVE_SIGACTION 1

/* Define to 1 if you have the `posix_memalign' function. */
#define HAVE_POSIX_MEMALIGN 1

/* Define to 1 if you have the `gettimeofday' function. */
#define HAVE_GETTIMEOFDAY 1

/* Define to 1 if you have the `feenableexcept' function. */
/* #undef HAVE_FEENABLEEXCEPT */

/* Define to 1 if you have the FE_DIVBYZERO symbol. */
#define HAVE_FEDIVBYZERO 1

/* Define to 1 if you have the `clz' builtin function. */
#define HAVE_BUILTIN_CLZ 1

/* Define to 1 if you have float128 support. */
/* #undef HAVE_FLOAT128 */

/* Whether the compiler supports GCC vector extensions */
#define HAVE_GCC_VECTOR_EXTENSIONS 1

/* Define to 1 if toolchain supports constructor attribute. */
#define TOOLCHAIN_SUPPORTS_ATTRIBUTE_CONSTRUCTOR 1

/* Define to 1 if toolchain supports destructor attribute. */
/* #undef TOOLCHAIN_SUPPORTS_ATTRIBUTE_DESTRUCTOR */

/* Define to 1 to use ARM NEON assembly optimizations */
/* #undef USE_ARM_NEON */

/* Define to 1 to use ARM A64 NEON assembly optimizations */
/* #undef USE_ARM_A64_NEON */

/* Define to 1 to use ARM SIMD assembly optimizations */
/* #undef USE_ARM_SIMD */

/* Define to 1 to use Loongson MMI optimizations */
/* #undef USE_LOONGSON_MMI */

/* Define to 1 to use MIPS DSPr2 optimizations */
/* #undef USE_MIPS_DSPR2 */

/* Define to 1 to use SSE2 optimizations */
/* #undef USE_SSE2 */

/* Define to 1 to use SSSE3 optimizations */
/* #undef USE_SSSE3 */

/* Define to 1 to use VMX/Altivec optimizations */
/* #undef USE_VMX */

/* Define to 1 to use x86 MMX optimizations */
/* #undef USE_X86_MMX */

/* Define to 1 to use RISC-V Vector optimizations */
/* #undef USE_RVV */

/* Define to 1 to use GNU inline assembly */
/* #undef USE_GCC_INLINE_ASM */

/* Define to 1 to use OpenMP */
/* #undef USE_OPENMP */

/* Define to 1 if ASM has .func directive */
/* #undef ASM_HAVE_FUNC_DIRECTIVE */

/* Define to 1 if ASM has .syntax unified directive */
/* #undef ASM_HAVE_SYNTAX_UNIFIED */

/* Define to 1 if ASM symbols have leading underscore */
/* #undef ASM_LEADING_UNDERSCORE */

/* Enable TIMER_* macros */
/* #undef PIXMAN_TIMERS */

/* Enable gnuplot output */
/* #undef PIXMAN_GNUPLOT */

/* Define to 1 if you have libpng */
#define HAVE_LIBPNG 1

/* The size of `long', as computed by sizeof. */
#define SIZEOF_LONG 8

/* Define WORDS_BIGENDIAN to 1 if your processor stores words with the most
   significant byte first (like Motorola and SPARC, unlike Intel). */
/* #undef WORDS_BIGENDIAN */

/* The TLS keyword */
/* #undef TLS */

/* Package name */
#define PACKAGE "pixman"

#endif /* PIXMAN_CONFIG_H */
