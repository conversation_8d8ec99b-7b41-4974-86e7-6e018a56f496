{"archive": {}, "artifacts": [{"path": "test/utils/libtestutils.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_compile_definitions", "target_include_directories"], "files": ["test/utils/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 23, "parent": 0}, {"command": 1, "file": 0, "line": 37, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 97, "parent": 3}, {"command": 3, "file": 0, "line": 29, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu99"}], "defines": [{"backtrace": 4, "define": "HAVE_CONFIG_H"}], "includes": [{"backtrace": 5, "path": "/Users/<USER>/work/pixman2/test/utils"}, {"backtrace": 5, "path": "/Users/<USER>/work/pixman2/build"}, {"backtrace": 2, "path": "/Users/<USER>/work/pixman2/pixman"}, {"backtrace": 2, "path": "/Users/<USER>/work/pixman2/build/pixman"}], "language": "C", "languageStandard": {"backtraces": [1], "standard": "99"}, "sourceIndexes": [0, 1]}], "dependencies": [{"backtrace": 2, "id": "pixman-1::@d2e911910b37690d273b"}], "id": "testutils::@1e67207dbfbf1b9d4649", "name": "testutils", "nameOnDisk": "libtestutils.a", "paths": {"build": "test/utils", "source": "test/utils"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "test/utils/utils.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "test/utils/utils-prng.c", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}