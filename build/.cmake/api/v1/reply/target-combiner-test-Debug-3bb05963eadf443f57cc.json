{"artifacts": [{"path": "test/combiner-test"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "set_target_properties", "find_package", "add_compile_definitions", "target_include_directories"], "files": ["test/CMakeLists.txt", "/usr/local/share/cmake/Modules/FindPNG.cmake", "CMakeLists.txt", "pixman/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 76, "parent": 0}, {"command": 1, "file": 0, "line": 79, "parent": 0}, {"command": 1, "file": 0, "line": 93, "parent": 0}, {"file": 2}, {"command": 3, "file": 2, "line": 122, "parent": 4}, {"file": 1, "parent": 5}, {"command": 2, "file": 1, "line": 137, "parent": 6}, {"file": 3}, {"command": 1, "file": 3, "line": 214, "parent": 8}, {"command": 4, "file": 2, "line": 97, "parent": 4}, {"command": 5, "file": 0, "line": 97, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu99"}], "defines": [{"backtrace": 10, "define": "HAVE_CONFIG_H"}], "includes": [{"backtrace": 11, "path": "/Users/<USER>/work/pixman2/build"}, {"backtrace": 2, "path": "/Users/<USER>/work/pixman2/pixman"}, {"backtrace": 2, "path": "/Users/<USER>/work/pixman2/build/pixman"}, {"backtrace": 2, "path": "/Users/<USER>/work/pixman2/test/utils"}], "language": "C", "languageStandard": {"backtraces": [1], "standard": "99"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 2, "id": "pixman-1::@d2e911910b37690d273b"}, {"backtrace": 2, "id": "testutils::@1e67207dbfbf1b9d4649"}], "id": "combiner-test::@36f028580bb02cc8272a", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"backtrace": 2, "fragment": "pixman/libpixman-1.a", "role": "libraries"}, {"backtrace": 2, "fragment": "test/utils/libtestutils.a", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/local/lib/libpng.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "pixman/libpixman-1.a", "role": "libraries"}, {"backtrace": 7, "fragment": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd", "role": "libraries"}, {"backtrace": 9, "fragment": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd", "role": "libraries"}], "language": "C"}, "name": "combiner-test", "nameOnDisk": "combiner-test", "paths": {"build": "test", "source": "test"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "test/combiner-test.c", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}