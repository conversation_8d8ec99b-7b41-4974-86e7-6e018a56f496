{"artifacts": [{"path": "demos/srgb-trap-test"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "set_target_properties", "find_package", "add_compile_definitions", "target_include_directories"], "files": ["demos/CMakeLists.txt", "/usr/local/share/cmake/Modules/FindPNG.cmake", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 93, "parent": 0}, {"command": 1, "file": 0, "line": 108, "parent": 0}, {"command": 1, "file": 0, "line": 127, "parent": 0}, {"file": 2}, {"command": 3, "file": 2, "line": 122, "parent": 4}, {"file": 1, "parent": 5}, {"command": 2, "file": 1, "line": 137, "parent": 6}, {"command": 1, "file": 0, "line": 83, "parent": 0}, {"command": 4, "file": 2, "line": 97, "parent": 4}, {"command": 5, "file": 0, "line": 99, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu99"}], "defines": [{"backtrace": 9, "define": "HAVE_CONFIG_H"}], "includes": [{"backtrace": 10, "path": "/Users/<USER>/work/pixman2/build"}, {"backtrace": 10, "path": "/Users/<USER>/work/pixman2/build/demos"}, {"backtrace": 10, "path": "/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0"}, {"backtrace": 10, "path": "/usr/local/Cellar/pango/1.56.3/include/pango-1.0"}, {"backtrace": 10, "path": "/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz"}, {"backtrace": 10, "path": "/usr/local/Cellar/graphite2/1.3.14/include"}, {"backtrace": 10, "path": "/usr/local/include/cairo"}, {"backtrace": 10, "path": "/usr/local/opt/freetype/include/freetype2"}, {"backtrace": 10, "path": "/usr/local/Cellar/pixman/0.46.0/include/pixman-1"}, {"backtrace": 10, "path": "/usr/local/include/gdk-pixbuf-2.0"}, {"backtrace": 10, "path": "/usr/local/opt/libpng/include/libpng16"}, {"backtrace": 10, "path": "/usr/local/opt/libtiff/include"}, {"backtrace": 10, "path": "/usr/local/opt/zstd/include"}, {"backtrace": 10, "path": "/usr/local/Cellar/xz/5.8.1/include"}, {"backtrace": 10, "path": "/usr/local/opt/jpeg-turbo/include"}, {"backtrace": 10, "path": "/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0"}, {"backtrace": 10, "path": "/usr/local/Cellar/libepoxy/1.5.10/include"}, {"backtrace": 10, "path": "/usr/local/Cellar/fribidi/1.0.16/include/fribidi"}, {"backtrace": 10, "path": "/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0"}, {"backtrace": 10, "path": "/usr/local/Cellar/glib/2.84.2/include"}, {"backtrace": 10, "path": "/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi"}, {"backtrace": 10, "path": "/usr/local/Cellar/glib/2.84.2/include/glib-2.0"}, {"backtrace": 10, "path": "/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include"}, {"backtrace": 10, "path": "/usr/local/opt/gettext/include"}, {"backtrace": 10, "path": "/usr/local/Cellar/pcre2/10.45/include"}, {"backtrace": 2, "path": "/Users/<USER>/work/pixman2/pixman"}, {"backtrace": 2, "path": "/Users/<USER>/work/pixman2/build/pixman"}, {"backtrace": 2, "path": "/Users/<USER>/work/pixman2/test/utils"}], "language": "C", "languageStandard": {"backtraces": [1], "standard": "99"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 2, "id": "pixman-1::@d2e911910b37690d273b"}, {"backtrace": 2, "id": "testutils::@1e67207dbfbf1b9d4649"}, {"backtrace": 2, "id": "demo::@a9e843653e3a5da897f6"}], "id": "srgb-trap-test::@a9e843653e3a5da897f6", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"backtrace": 2, "fragment": "demos/libdemo.a", "role": "libraries"}, {"backtrace": 2, "fragment": "pixman/libpixman-1.a", "role": "libraries"}, {"backtrace": 2, "fragment": "test/utils/libtestutils.a", "role": "libraries"}, {"backtrace": 2, "fragment": "-lgtk-3", "role": "libraries"}, {"backtrace": 2, "fragment": "-lgdk-3", "role": "libraries"}, {"backtrace": 2, "fragment": "-lpangocairo-1.0", "role": "libraries"}, {"backtrace": 2, "fragment": "-lpango-1.0", "role": "libraries"}, {"backtrace": 2, "fragment": "-lharfbuzz", "role": "libraries"}, {"backtrace": 2, "fragment": "-lcairo-gobject", "role": "libraries"}, {"backtrace": 2, "fragment": "-l<PERSON><PERSON>", "role": "libraries"}, {"backtrace": 2, "fragment": "-lm", "role": "libraries"}, {"backtrace": 2, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 2, "fragment": "-lfontconfig", "role": "libraries"}, {"backtrace": 2, "fragment": "-lfreetype", "role": "libraries"}, {"backtrace": 2, "fragment": "pixman/libpixman-1.a", "role": "libraries"}, {"backtrace": 2, "fragment": "-lgdk_pixbuf-2.0", "role": "libraries"}, {"backtrace": 2, "fragment": "-lpng16", "role": "libraries"}, {"backtrace": 2, "fragment": "-latk-1.0", "role": "libraries"}, {"backtrace": 2, "fragment": "-lgio-2.0", "role": "libraries"}, {"backtrace": 2, "fragment": "-lgobject-2.0", "role": "libraries"}, {"backtrace": 2, "fragment": "-lglib-2.0", "role": "libraries"}, {"backtrace": 2, "fragment": "-lintl", "role": "libraries"}, {"backtrace": 2, "fragment": "-lz", "role": "libraries"}, {"backtrace": 2, "fragment": "-framework CoreFoundation", "role": "libraries"}, {"backtrace": 2, "fragment": "-framework ApplicationServices", "role": "libraries"}, {"backtrace": 2, "fragment": "-lglib-2.0", "role": "libraries"}, {"backtrace": 2, "fragment": "-lintl", "role": "libraries"}, {"backtrace": 3, "fragment": "/usr/local/lib/libpng.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "-lz", "role": "libraries"}, {"backtrace": 2, "fragment": "-framework CoreFoundation", "role": "libraries"}, {"backtrace": 2, "fragment": "-framework ApplicationServices", "role": "libraries"}, {"backtrace": 7, "fragment": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd", "role": "libraries"}, {"backtrace": 8, "fragment": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd", "role": "libraries"}], "language": "C"}, "name": "srgb-trap-test", "nameOnDisk": "srgb-trap-test", "paths": {"build": "demos", "source": "demos"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "demos/srgb-trap-test.c", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}