{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.2/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Darwin-Initialize.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.2/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Darwin.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Compiler/AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Apple-Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Apple-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Linker/AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Linker/AppleClang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckFunctionExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckSymbolExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckCSourceRuns.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/Internal/CheckSourceRuns.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckTypeSize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckIncludeFileCXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/TestBigEndian.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckTypeSize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckIncludeFileCXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/GNUInstallDirs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPkgConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindOpenMP.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPNG.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindZLIB.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/SelectLibraryConfigurations.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/SelectLibraryConfigurations.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/FindPackageMessage.cmake"}, {"path": "cmake/PixmanSIMD.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/local/share/cmake/Modules/CheckCSourceCompiles.cmake"}, {"path": "cmake/config.h.in"}, {"path": "pixman-1.pc.in"}, {"path": "pixman/CMakeLists.txt"}, {"path": "pixman/pixman-version.h.in"}, {"path": "test/utils/CMakeLists.txt"}, {"path": "demos/CMakeLists.txt"}, {"path": "test/CMakeLists.txt"}], "kind": "cmakeFiles", "paths": {"build": "/Users/<USER>/work/pixman2/build", "source": "/Users/<USER>/work/pixman2"}, "version": {"major": 1, "minor": 1}}