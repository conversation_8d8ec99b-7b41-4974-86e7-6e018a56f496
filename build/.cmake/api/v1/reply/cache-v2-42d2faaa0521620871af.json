{"entries": [{"name": "ASM_HAVE_FUNC_DIRECTIVE", "properties": [{"name": "HELPSTRING", "value": "Test ASM_HAVE_FUNC_DIRECTIVE"}], "type": "INTERNAL", "value": ""}, {"name": "ASM_HAVE_SYNTAX_UNIFIED", "properties": [{"name": "HELPSTRING", "value": "Test ASM_HAVE_SYNTAX_UNIFIED"}], "type": "INTERNAL", "value": ""}, {"name": "ASM_LEADING_UNDERSCORE", "properties": [{"name": "HELPSTRING", "value": "Test ASM_LEADING_UNDERSCORE"}], "type": "INTERNAL", "value": ""}, {"name": "ASM_LEADING_UNDERSCORE_COMPILED", "properties": [{"name": "HELPSTRING", "value": "Result of TRY_COMPILE"}], "type": "INTERNAL", "value": "FALSE"}, {"name": "CMAKE_ADDR2LINE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_ADDR2LINE-NOTFOUND"}, {"name": "CMAKE_AR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/ar"}, {"name": "CMAKE_BUILD_TYPE", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "STRING", "value": "Debug"}, {"name": "CMAKE_CACHEFILE_DIR", "properties": [{"name": "HELPSTRING", "value": "This is the directory where this CMakeCache.txt was created"}], "type": "INTERNAL", "value": "/Users/<USER>/work/pixman2/build"}, {"name": "CMAKE_CACHE_MAJOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Major version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "4"}, {"name": "CMAKE_CACHE_MINOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Minor version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "0"}, {"name": "CMAKE_CACHE_PATCH_VERSION", "properties": [{"name": "HELPSTRING", "value": "Patch version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "2"}, {"name": "CMAKE_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to CMake executable."}], "type": "INTERNAL", "value": "/usr/local/bin/cmake"}, {"name": "CMAKE_CPACK_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to cpack program executable."}], "type": "INTERNAL", "value": "/usr/local/bin/cpack"}, {"name": "CMAKE_CTEST_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to ctest program executable."}], "type": "INTERNAL", "value": "/usr/local/bin/ctest"}, {"name": "CMAKE_CXX_COMPILER", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "FILEPATH", "value": "/usr/bin/clang++"}, {"name": "CMAKE_C_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "FILEPATH", "value": "/usr/bin/clang"}, {"name": "CMAKE_C_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_C_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during DEBUG builds."}], "type": "STRING", "value": "-g"}, {"name": "CMAKE_C_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during MINSIZEREL builds."}], "type": "STRING", "value": "-Os -DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELEASE builds."}], "type": "STRING", "value": "-O3 -DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "-O2 -g -DNDEBUG"}, {"name": "CMAKE_DLLTOOL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_DLLTOOL-NOTFOUND"}, {"name": "CMAKE_EDIT_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to cache edit program executable."}], "type": "INTERNAL", "value": "/usr/local/bin/ccmake"}, {"name": "CMAKE_EXECUTABLE_FORMAT", "properties": [{"name": "HELPSTRING", "value": "Executable file format"}], "type": "INTERNAL", "value": "MACHO"}, {"name": "CMAKE_EXE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXPORT_BUILD_DATABASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable/Disable output of build database during the build."}], "type": "BOOL", "value": ""}, {"name": "CMAKE_EXPORT_COMPILE_COMMANDS", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "BOOL", "value": "TRUE"}, {"name": "CMAKE_EXTRA_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of external makefile project generator."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_FIND_PACKAGE_REDIRECTS_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake."}], "type": "STATIC", "value": "/Users/<USER>/work/pixman2/build/CMakeFiles/pkgRedirects"}, {"name": "CMAKE_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "Name of generator."}], "type": "INTERNAL", "value": "Ninja"}, {"name": "CMAKE_GENERATOR_INSTANCE", "properties": [{"name": "HELPSTRING", "value": "Generator instance identifier."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR_PLATFORM", "properties": [{"name": "HELPSTRING", "value": "Name of generator platform."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR_TOOLSET", "properties": [{"name": "HELPSTRING", "value": "Name of generator toolset."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HAVE_LIBC_PTHREAD", "properties": [{"name": "HELPSTRING", "value": "Test CMAKE_HAVE_LIBC_PTHREAD"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_HOME_DIRECTORY", "properties": [{"name": "HELPSTRING", "value": "Source directory with the top level CMakeLists.txt file for this project"}], "type": "INTERNAL", "value": "/Users/<USER>/work/pixman2"}, {"name": "CMAKE_INSTALL_BINDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "User executables (bin)"}], "type": "PATH", "value": "bin"}, {"name": "CMAKE_INSTALL_DATADIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only architecture-independent data (DATAROOTDIR)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_DATAROOTDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only architecture-independent data root (share)"}], "type": "PATH", "value": "share"}, {"name": "CMAKE_INSTALL_DOCDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Documentation root (DATAROOTDIR/doc/PROJECT_NAME)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_INCLUDEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C header files (include)"}], "type": "PATH", "value": "include"}, {"name": "CMAKE_INSTALL_INFODIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Info documentation (DATAROOTDIR/info)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_LIBDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Object code libraries (lib)"}], "type": "PATH", "value": "lib"}, {"name": "CMAKE_INSTALL_LIBEXECDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Program executables (libexec)"}], "type": "PATH", "value": "libexec"}, {"name": "CMAKE_INSTALL_LOCALEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Locale-dependent data (DATAROOTDIR/locale)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_LOCALSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Modifiable single-machine data (var)"}], "type": "PATH", "value": "var"}, {"name": "CMAKE_INSTALL_MANDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Man documentation (DATAROOTDIR/man)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_NAME_TOOL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/install_name_tool"}, {"name": "CMAKE_INSTALL_OLDINCLUDEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C header files for non-gcc (/usr/include)"}], "type": "PATH", "value": "/usr/include"}, {"name": "CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "Install path prefix, prepended onto install directories."}], "type": "PATH", "value": "/usr/local"}, {"name": "CMAKE_INSTALL_RUNSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Run-time variable data (LOCALSTATEDIR/run)"}], "type": "PATH", "value": ""}, {"name": "CMAKE_INSTALL_SBINDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "System admin executables (sbin)"}], "type": "PATH", "value": "sbin"}, {"name": "CMAKE_INSTALL_SHAREDSTATEDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Modifiable architecture-independent data (com)"}], "type": "PATH", "value": "com"}, {"name": "CMAKE_INSTALL_SYSCONFDIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Read-only single-machine data (etc)"}], "type": "PATH", "value": "etc"}, {"name": "CMAKE_LINKER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/ld"}, {"name": "CMAKE_LIST_FILE_NAME", "properties": [{"name": "HELPSTRING", "value": "Name of CMakeLists files to read"}], "type": "INTERNAL", "value": "CMakeLists.txt"}, {"name": "CMAKE_MAKE_PROGRAM", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Program used to build from build.ninja files."}], "type": "FILEPATH", "value": "/usr/local/bin/ninja"}, {"name": "CMAKE_MODULE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_NM", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/nm"}, {"name": "CMAKE_NUMBER_OF_MAKEFILES", "properties": [{"name": "HELPSTRING", "value": "number of local generators"}], "type": "INTERNAL", "value": "5"}, {"name": "CMAKE_OBJCOPY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_OBJCOPY-NOTFOUND"}, {"name": "CMAKE_OBJDUMP", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/objdump"}, {"name": "CMAKE_OSX_ARCHITECTURES", "properties": [{"name": "HELPSTRING", "value": "Build architectures for OSX"}], "type": "STRING", "value": ""}, {"name": "CMAKE_OSX_DEPLOYMENT_TARGET", "properties": [{"name": "HELPSTRING", "value": "Minimum OS X version to target for deployment (at runtime); newer APIs weak linked. Set to empty string for default value."}], "type": "STRING", "value": ""}, {"name": "CMAKE_OSX_SYSROOT", "properties": [{"name": "HELPSTRING", "value": "The product will be built against the headers and libraries located inside the indicated SDK."}], "type": "STRING", "value": ""}, {"name": "CMAKE_PLATFORM_INFO_INITIALIZED", "properties": [{"name": "HELPSTRING", "value": "Platform information initialized"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_PROJECT_DESCRIPTION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "The pixman library (version 1)"}, {"name": "CMAKE_PROJECT_HOMEPAGE_URL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_NAME", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "pixman"}, {"name": "CMAKE_PROJECT_VERSION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "0.46.5"}, {"name": "CMAKE_PROJECT_VERSION_MAJOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "0"}, {"name": "CMAKE_PROJECT_VERSION_MINOR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "46"}, {"name": "CMAKE_PROJECT_VERSION_PATCH", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "5"}, {"name": "CMAKE_PROJECT_VERSION_TWEAK", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_RANLIB", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/ranlib"}, {"name": "CMAKE_READELF", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_READELF-NOTFOUND"}, {"name": "CMAKE_ROOT", "properties": [{"name": "HELPSTRING", "value": "Path to CMake installation."}], "type": "INTERNAL", "value": "/usr/local/share/cmake"}, {"name": "CMAKE_SHARED_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SKIP_INSTALL_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when installing shared libraries, but are added when building."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_SKIP_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when using shared libraries."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_STATIC_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STRIP", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/strip"}, {"name": "CMAKE_TAPI", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/tapi"}, {"name": "CMAKE_UNAME", "properties": [{"name": "HELPSTRING", "value": "uname command"}], "type": "INTERNAL", "value": "/usr/bin/uname"}, {"name": "CMAKE_VERBOSE_MAKEFILE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If this value is on, makefiles will be generated without the .SILENT directive, and all commands will be echoed to the console during the make.  This is useful for debugging only. With Visual Studio IDE projects all commands are done without /nologo."}], "type": "BOOL", "value": "FALSE"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PNG", "properties": [{"name": "HELPSTRING", "value": "Details about finding PNG"}], "type": "INTERNAL", "value": "[/usr/local/lib/libpng.dylib][/usr/local/include][v1.6.48()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig", "properties": [{"name": "HELPSTRING", "value": "Details about finding PkgConfig"}], "type": "INTERNAL", "value": "[/usr/local/bin/pkg-config][v2.4.3()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_Threads", "properties": [{"name": "HELPSTRING", "value": "Details about finding Threads"}], "type": "INTERNAL", "value": "[TRUE][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_ZLIB", "properties": [{"name": "HELPSTRING", "value": "Details about finding ZLIB"}], "type": "INTERNAL", "value": "[/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd][/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include][ ][v1.2.11()]"}, {"name": "GLIB2_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/local/Cellar/glib/2.84.2/include/glib-2.0;-I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include;-I/usr/local/opt/gettext/include;-I/usr/local/Cellar/pcre2/10.45/include"}, {"name": "GLIB2_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GLIB2_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GLIB2_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "GLIB2_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/local/Cellar/glib/2.84.2/include"}, {"name": "GLIB2_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/local/Cellar/glib/2.84.2/include/glib-2.0;/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include;/usr/local/opt/gettext/include;/usr/local/Cellar/pcre2/10.45/include"}, {"name": "GLIB2_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/local/Cellar/glib/2.84.2/lib;-lglib-2.0;-L/usr/local/opt/gettext/lib;-lintl"}, {"name": "GLIB2_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GLIB2_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/local/Cellar/glib/2.84.2/lib"}, {"name": "GLIB2_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "glib-2.0;intl"}, {"name": "GLIB2_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/local/Cellar/glib/2.84.2/lib;/usr/local/opt/gettext/lib"}, {"name": "GLIB2_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GLIB2_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GLIB2_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GLIB2_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GLIB2_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "glib-2.0"}, {"name": "GLIB2_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/local/Cellar/glib/2.84.2"}, {"name": "GLIB2_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/local/Cellar/glib/2.84.2/include/glib-2.0;-I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include;-I/usr/local/opt/gettext/include;-I/usr/local/Cellar/pcre2/10.45/include"}, {"name": "GLIB2_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GLIB2_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GLIB2_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/local/Cellar/glib/2.84.2/include/glib-2.0;/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include;/usr/local/opt/gettext/include;/usr/local/Cellar/pcre2/10.45/include"}, {"name": "GLIB2_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/local/Cellar/glib/2.84.2/lib;-lglib-2.0;-L/usr/local/opt/gettext/lib;-lintl;-liconv;-lm;-framework;Foundation;-framework;CoreFoundation;-framework;AppKit;-framework;Carbon;-L/usr/local/Cellar/pcre2/10.45/lib;-lpcre2-8;-D_THREAD_SAFE;-pthread"}, {"name": "GLIB2_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-framework;Foundation;-framework;CoreFoundation;-framework;AppKit;-framework;Carbon;-D_THREAD_SAFE;-pthread"}, {"name": "GLIB2_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GLIB2_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "glib-2.0;intl;iconv;m;pcre2-8"}, {"name": "GLIB2_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/local/Cellar/glib/2.84.2/lib;/usr/local/opt/gettext/lib;/usr/local/Cellar/pcre2/10.45/lib"}, {"name": "GLIB2_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GLIB2_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GLIB2_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GLIB2_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GLIB2_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "2.84.2"}, {"name": "GLIB2_glib-2.0_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GLIB2_glib-2.0_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GLIB2_glib-2.0_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GLIB2_glib-2.0_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GTK3_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0;-I/usr/local/Cellar/pango/1.56.3/include/pango-1.0;-I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz;-I/usr/local/Cellar/graphite2/1.3.14/include;-I/usr/local/include;-I/usr/local/include/cairo;-I/usr/local/opt/freetype/include/freetype2;-I/usr/local/Cellar/pixman/0.46.0/include/pixman-1;-I/usr/local/include/gdk-pixbuf-2.0;-I/usr/local/opt/libpng/include/libpng16;-I/usr/local/opt/libtiff/include;-I/usr/local/opt/zstd/include;-I/usr/local/Cellar/xz/5.8.1/include;-I/usr/local/opt/jpeg-turbo/include;-I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0;-I/usr/local/Cellar/libepoxy/1.5.10/include;-I/usr/local/Cellar/fribidi/1.0.16/include/fribidi;-I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0;-I/usr/local/Cellar/glib/2.84.2/include;-I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi;-I/usr/local/Cellar/glib/2.84.2/include/glib-2.0;-I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include;-I/usr/local/opt/gettext/include;-I/usr/local/Cellar/pcre2/10.45/include"}, {"name": "GTK3_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GTK3_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GTK3_FOUND", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "GTK3_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/local/Cellar/gtk+3/3.24.43/include"}, {"name": "GTK3_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0;/usr/local/Cellar/pango/1.56.3/include/pango-1.0;/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz;/usr/local/Cellar/graphite2/1.3.14/include;/usr/local/include;/usr/local/include/cairo;/usr/local/opt/freetype/include/freetype2;/usr/local/Cellar/pixman/0.46.0/include/pixman-1;/usr/local/include/gdk-pixbuf-2.0;/usr/local/opt/libpng/include/libpng16;/usr/local/opt/libtiff/include;/usr/local/opt/zstd/include;/usr/local/Cellar/xz/5.8.1/include;/usr/local/opt/jpeg-turbo/include;/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0;/usr/local/Cellar/libepoxy/1.5.10/include;/usr/local/Cellar/fribidi/1.0.16/include/fribidi;/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0;/usr/local/Cellar/glib/2.84.2/include;/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi;/usr/local/Cellar/glib/2.84.2/include/glib-2.0;/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include;/usr/local/opt/gettext/include;/usr/local/Cellar/pcre2/10.45/include"}, {"name": "GTK3_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/local/Cellar/gtk+3/3.24.43/lib;-lgtk-3;-lgdk-3;-Wl,-framework,Cocoa;-Wl,-framework,Carbon;-Wl,-framework,CoreGraphics;-L/usr/local/Cellar/pango/1.56.3/lib;-lpangocairo-1.0;-lpango-1.0;-L/usr/local/Cellar/harfbuzz/11.1.0/lib;-lharfbuzz;-L/usr/local/lib;-lcairo-gobject;-lcairo;-lm;-ldl;-framework;CoreFoundation;-framework;ApplicationServices;-lfontconfig;-L/usr/local/opt/freetype/lib;-lfreetype;-L/usr/local/Cellar/pixman/0.46.0/lib;-lpixman-1;-lgdk_pixbuf-2.0;-L/usr/local/opt/libpng/lib;-lpng16;-L/usr/local/Cellar/at-spi2-core/2.56.1/lib;-latk-1.0;-L/usr/local/Cellar/glib/2.84.2/lib;-lgio-2.0;-lgobject-2.0;-lglib-2.0;-L/usr/local/opt/gettext/lib;-lintl;-L/usr/lib;-lz"}, {"name": "GTK3_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-Wl,-framework,Cocoa;-Wl,-framework,Carbon;-Wl,-framework,CoreGraphics"}, {"name": "GTK3_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/local/Cellar/gtk+3/3.24.43/lib"}, {"name": "GTK3_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "gtk-3;gdk-3;pangocairo-1.0;pango-1.0;harfbuzz;cairo-gobject;cairo;m;dl;fontconfig;freetype;pixman-1;gdk_pixbuf-2.0;png16;atk-1.0;gio-2.0;gobject-2.0;glib-2.0;intl;z;-framework CoreFoundation;-framework ApplicationServices"}, {"name": "GTK3_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/local/Cellar/gtk+3/3.24.43/lib;/usr/local/Cellar/pango/1.56.3/lib;/usr/local/Cellar/harfbuzz/11.1.0/lib;/usr/local/lib;/usr/local/opt/freetype/lib;/usr/local/Cellar/pixman/0.46.0/lib;/usr/local/opt/libpng/lib;/usr/local/Cellar/at-spi2-core/2.56.1/lib;/usr/local/Cellar/glib/2.84.2/lib;/usr/local/opt/gettext/lib;/usr/lib"}, {"name": "GTK3_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GTK3_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GTK3_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GTK3_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GTK3_MODULE_NAME", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "gtk+-3.0"}, {"name": "GTK3_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/local/Cellar/gtk+3/3.24.43"}, {"name": "GTK3_STATIC_CFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-I/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0;-I/usr/local/Cellar/pango/1.56.3/include/pango-1.0;-I/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz;-I/usr/local/Cellar/graphite2/1.3.14/include;-I/usr/local/include;-I/usr/local/include/cairo;-I/usr/local/opt/freetype/include/freetype2;-I/usr/local/Cellar/pixman/0.46.0/include/pixman-1;-I/usr/local/include/gdk-pixbuf-2.0;-I/usr/local/opt/libpng/include/libpng16;-I/usr/local/opt/libtiff/include;-I/usr/local/opt/zstd/include;-I/usr/local/Cellar/xz/5.8.1/include;-I/usr/local/opt/jpeg-turbo/include;-I/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0;-I/usr/local/Cellar/libepoxy/1.5.10/include;-I/usr/local/Cellar/fribidi/1.0.16/include/fribidi;-I/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0;-I/usr/local/Cellar/glib/2.84.2/include;-I/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi;-I/usr/local/Cellar/glib/2.84.2/include/glib-2.0;-I/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include;-I/usr/local/opt/gettext/include;-I/usr/local/Cellar/pcre2/10.45/include;-DLZMA_API_STATIC;-DFRIBIDI_LIB_STATIC"}, {"name": "GTK3_STATIC_CFLAGS_I", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GTK3_STATIC_CFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-DLZMA_API_STATIC;-DFRIBIDI_LIB_STATIC"}, {"name": "GTK3_STATIC_INCLUDE_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0;/usr/local/Cellar/pango/1.56.3/include/pango-1.0;/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz;/usr/local/Cellar/graphite2/1.3.14/include;/usr/local/include;/usr/local/include/cairo;/usr/local/opt/freetype/include/freetype2;/usr/local/Cellar/pixman/0.46.0/include/pixman-1;/usr/local/include/gdk-pixbuf-2.0;/usr/local/opt/libpng/include/libpng16;/usr/local/opt/libtiff/include;/usr/local/opt/zstd/include;/usr/local/Cellar/xz/5.8.1/include;/usr/local/opt/jpeg-turbo/include;/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0;/usr/local/Cellar/libepoxy/1.5.10/include;/usr/local/Cellar/fribidi/1.0.16/include/fribidi;/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0;/usr/local/Cellar/glib/2.84.2/include;/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi;/usr/local/Cellar/glib/2.84.2/include/glib-2.0;/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include;/usr/local/opt/gettext/include;/usr/local/Cellar/pcre2/10.45/include"}, {"name": "GTK3_STATIC_LDFLAGS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-L/usr/local/Cellar/gtk+3/3.24.43/lib;-lgtk-3;-lgdk-3;-Wl,-framework,Cocoa;-Wl,-framework,Carbon;-Wl,-framework,CoreGraphics;-L/usr/local/Cellar/pango/1.56.3/lib;-lpangocairo-1.0;-lm;-framework;CoreFoundation;-framework;ApplicationServices;-lpangoft2-1.0;-lm;-framework;CoreFoundation;-framework;ApplicationServices;-lpango-1.0;-lm;-framework;CoreFoundation;-framework;ApplicationServices;-L/usr/local/Cellar/harfbuzz/11.1.0/lib;-lharfbuzz-gobject;-lharfbuzz;-framework;ApplicationServices;-L/usr/local/Cellar/graphite2/1.3.14/lib;-lgraphite2;-framework;CoreFoundation;-framework;ApplicationServices;-L/usr/local/lib;-lcairo-gobject;-lcairo;-lm;-ldl;-framework;CoreFoundation;-framework;ApplicationServices;-lfontconfig;-lintl;-lm;-L/usr/lib;-lexpat;-L/usr/local/opt/freetype/lib;-lfreetype;-lbz2;-L/usr/local/Cellar/pixman/0.46.0/lib;-lpixman-1;-lm;-lgdk_pixbuf-2.0;-lm;-lintl;-L/usr/local/opt/libpng/lib;-lpng16;-lz;-L/usr/local/opt/libtiff/lib;-ltiff;-L/usr/local/opt/zstd/lib;-lzstd;-L/usr/local/Cellar/xz/5.8.1/lib;-llzma;-pthread;-lpthread;-L/usr/local/opt/jpeg-turbo/lib;-ljpeg;-L/usr/local/Cellar/at-spi2-core/2.56.1/lib;-latk-1.0;-L/usr/local/Cellar/libepoxy/1.5.10/lib;-lepoxy;-ldl;-L/usr/local/Cellar/fribidi/1.0.16/lib;-lfribidi;-L/usr/local/Cellar/glib/2.84.2/lib;-lgio-2.0;-lintl;-framework;Foundation;-framework;CoreFoundation;-framework;AppKit;-lresolv;-lgobject-2.0;-lintl;-lffi;-lgmodule-2.0;-lglib-2.0;-L/usr/local/opt/gettext/lib;-lintl;-liconv;-lm;-framework;Foundation;-framework;CoreFoundation;-framework;AppKit;-framework;Carbon;-L/usr/local/Cellar/pcre2/10.45/lib;-lpcre2-8;-D_THREAD_SAFE;-pthread;-lz"}, {"name": "GTK3_STATIC_LDFLAGS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "-Wl,-framework,Cocoa;-Wl,-framework,Carbon;-Wl,-framework,CoreGraphics;-framework;CoreFoundation;-framework;ApplicationServices;-framework;CoreFoundation;-framework;ApplicationServices;-framework;CoreFoundation;-framework;ApplicationServices;-framework;ApplicationServices;-framework;CoreFoundation;-framework;ApplicationServices;-framework;CoreFoundation;-framework;ApplicationServices;-pthread;-framework;Foundation;-framework;CoreFoundation;-framework;AppKit;-lresolv;-framework;Foundation;-framework;CoreFoundation;-framework;AppKit;-framework;Carbon;-D_THREAD_SAFE;-pthread"}, {"name": "GTK3_STATIC_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GTK3_STATIC_LIBRARIES", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "gtk-3;gdk-3;pangocairo-1.0;m;pangoft2-1.0;m;pango-1.0;m;harfbuzz-gobject;harfbuzz;graphite2;cairo-gobject;cairo;m;dl;fontconfig;intl;m;expat;freetype;bz2;pixman-1;m;gdk_pixbuf-2.0;m;intl;png16;z;tiff;zstd;lzma;pthread;jpeg;atk-1.0;epoxy;dl;fribidi;gio-2.0;intl;gobject-2.0;intl;ffi;gmodule-2.0;glib-2.0;intl;iconv;m;pcre2-8;z"}, {"name": "GTK3_STATIC_LIBRARY_DIRS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/local/Cellar/gtk+3/3.24.43/lib;/usr/local/Cellar/pango/1.56.3/lib;/usr/local/Cellar/harfbuzz/11.1.0/lib;/usr/local/Cellar/graphite2/1.3.14/lib;/usr/local/lib;/usr/lib;/usr/local/opt/freetype/lib;/usr/local/Cellar/pixman/0.46.0/lib;/usr/local/opt/libpng/lib;/usr/local/opt/libtiff/lib;/usr/local/opt/zstd/lib;/usr/local/Cellar/xz/5.8.1/lib;/usr/local/opt/jpeg-turbo/lib;/usr/local/Cellar/at-spi2-core/2.56.1/lib;/usr/local/Cellar/libepoxy/1.5.10/lib;/usr/local/Cellar/fribidi/1.0.16/lib;/usr/local/Cellar/glib/2.84.2/lib;/usr/local/opt/gettext/lib;/usr/local/Cellar/pcre2/10.45/lib"}, {"name": "GTK3_STATIC_LIBS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GTK3_STATIC_LIBS_L", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GTK3_STATIC_LIBS_OTHER", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GTK3_STATIC_LIBS_PATHS", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GTK3_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "3.24.43"}, {"name": "GTK3_gtk+-3.0_INCLUDEDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GTK3_gtk+-3.0_LIBDIR", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GTK3_gtk+-3.0_PREFIX", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "GTK3_gtk+-3.0_VERSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": ""}, {"name": "HAVE_ALARM", "properties": [{"name": "HELPSTRING", "value": "Have function alarm"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_BUILTIN_CLZ", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_BUILTIN_CLZ"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_FEDIVBYZERO", "properties": [{"name": "HELPSTRING", "value": "Have symbol FE_DIVBYZERO"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_FEENABLEEXCEPT", "properties": [{"name": "HELPSTRING", "value": "Have symbol feenableexcept"}], "type": "INTERNAL", "value": ""}, {"name": "HAVE_FENV_H", "properties": [{"name": "HELPSTRING", "value": "Have include fenv.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_FLOAT128", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_FLOAT128"}], "type": "INTERNAL", "value": ""}, {"name": "HAVE_GCC_VECTOR_EXTENSIONS", "properties": [{"name": "HELPSTRING", "value": "Test HAVE_GCC_VECTOR_EXTENSIONS"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_GETISAX", "properties": [{"name": "HELPSTRING", "value": "Have function getisax"}], "type": "INTERNAL", "value": ""}, {"name": "HAVE_GETPAGESIZE", "properties": [{"name": "HELPSTRING", "value": "Have function getpagesize"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_GETTIMEOFDAY", "properties": [{"name": "HELPSTRING", "value": "Have function gettimeofday"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_MMAP", "properties": [{"name": "HELPSTRING", "value": "Have function mmap"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_MPROTECT", "properties": [{"name": "HELPSTRING", "value": "Have function mprotect"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_POSIX_MEMALIGN", "properties": [{"name": "HELPSTRING", "value": "Have function posix_memalign"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_PTHREAD_H", "properties": [{"name": "HELPSTRING", "value": "Have include pthread.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_SIGACTION", "properties": [{"name": "HELPSTRING", "value": "Have function sigaction"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_SIZEOF_LONG", "properties": [{"name": "HELPSTRING", "value": "Result of TRY_COMPILE"}], "type": "INTERNAL", "value": "TRUE"}, {"name": "HAVE_STDDEF_H", "properties": [{"name": "HELPSTRING", "value": "Have include stddef.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_STDINT_H", "properties": [{"name": "HELPSTRING", "value": "Have include stdint.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_SYS_MMAN_H", "properties": [{"name": "HELPSTRING", "value": "Have include sys/mman.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_SYS_TYPES_H", "properties": [{"name": "HELPSTRING", "value": "Have include sys/types.h"}], "type": "INTERNAL", "value": "1"}, {"name": "HAVE_UNISTD_H", "properties": [{"name": "HELPSTRING", "value": "Have include unistd.h"}], "type": "INTERNAL", "value": "1"}, {"name": "MATH_LIBRARY", "properties": [{"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd"}, {"name": "OpenMP_COMPILE_RESULT_C_Xclang fopenmp", "properties": [{"name": "HELPSTRING", "value": "Result of TRY_COMPILE"}], "type": "INTERNAL", "value": "FALSE"}, {"name": "OpenMP_C_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C compiler flags for OpenMP parallelization"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OpenMP_C_LIB_NAMES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C compiler libraries for OpenMP parallelization"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OpenMP_libomp_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "OpenMP_libomp_LIBRARY-NOTFOUND"}, {"name": "PIXMAN_BUILD_DEMOS", "properties": [{"name": "HELPSTRING", "value": "Build demos"}], "type": "BOOL", "value": "ON"}, {"name": "PIXMAN_BUILD_TESTS", "properties": [{"name": "HELPSTRING", "value": "Build tests"}], "type": "BOOL", "value": "ON"}, {"name": "PIXMAN_CPU_FEATURES_PATH", "properties": [{"name": "HELPSTRING", "value": "Path to platform-specific cpu-features.[ch] for systems that do not provide it (e.g. Android)"}], "type": "STRING", "value": ""}, {"name": "PIXMAN_ENABLE_A64_NEON", "properties": [{"name": "HELPSTRING", "value": "Use ARM A64 NEON intrinsic optimized paths"}], "type": "BOOL", "value": "OFF"}, {"name": "PIXMAN_ENABLE_ARM_SIMD", "properties": [{"name": "HELPSTRING", "value": "Use ARMv6 SIMD intrinsic optimized paths"}], "type": "BOOL", "value": "OFF"}, {"name": "PIXMAN_ENABLE_GNUPLOT", "properties": [{"name": "HELPSTRING", "value": "Enable output of filters that can be piped to gnuplot"}], "type": "BOOL", "value": "OFF"}, {"name": "PIXMAN_ENABLE_GNU_INLINE_ASM", "properties": [{"name": "HELPSTRING", "value": "Use GNU style inline assembler"}], "type": "BOOL", "value": "OFF"}, {"name": "PIXMAN_ENABLE_GTK", "properties": [{"name": "HELPSTRING", "value": "Enable demos using GTK"}], "type": "BOOL", "value": "ON"}, {"name": "PIXMAN_ENABLE_LIBPNG", "properties": [{"name": "HELPSTRING", "value": "Use libpng in tests"}], "type": "BOOL", "value": "ON"}, {"name": "PIXMAN_ENABLE_LOONGSON_MMI", "properties": [{"name": "HELPSTRING", "value": "Use Loongson MMI intrinsic optimized paths"}], "type": "BOOL", "value": "OFF"}, {"name": "PIXMAN_ENABLE_MIPS_DSPR2", "properties": [{"name": "HELPSTRING", "value": "Use MIPS32 DSPr2 intrinsic optimized paths"}], "type": "BOOL", "value": "OFF"}, {"name": "PIXMAN_ENABLE_MMX", "properties": [{"name": "HELPSTRING", "value": "Use X86 MMX intrinsic optimized paths"}], "type": "BOOL", "value": "OFF"}, {"name": "PIXMAN_ENABLE_NEON", "properties": [{"name": "HELPSTRING", "value": "Use ARM NEON intrinsic optimized paths"}], "type": "BOOL", "value": "OFF"}, {"name": "PIXMAN_ENABLE_OPENMP", "properties": [{"name": "HELPSTRING", "value": "Enable OpenMP for tests"}], "type": "BOOL", "value": "ON"}, {"name": "PIXMAN_ENABLE_RVV", "properties": [{"name": "HELPSTRING", "value": "Use RISC-V Vector extension"}], "type": "BOOL", "value": "OFF"}, {"name": "PIXMAN_ENABLE_SSE2", "properties": [{"name": "HELPSTRING", "value": "Use X86 SSE2 intrinsic optimized paths"}], "type": "BOOL", "value": "OFF"}, {"name": "PIXMAN_ENABLE_SSSE3", "properties": [{"name": "HELPSTRING", "value": "Use X86 SSSE3 intrinsic optimized paths"}], "type": "BOOL", "value": "OFF"}, {"name": "PIXMAN_ENABLE_TIMERS", "properties": [{"name": "HELPSTRING", "value": "Enable TIMER_* macros"}], "type": "BOOL", "value": "OFF"}, {"name": "PIXMAN_ENABLE_TLS", "properties": [{"name": "HELPSTRING", "value": "Use compiler support for thread-local storage"}], "type": "BOOL", "value": "OFF"}, {"name": "PIXMAN_ENABLE_VMX", "properties": [{"name": "HELPSTRING", "value": "Use PPC VMX/Altivec intrinsic optimized paths"}], "type": "BOOL", "value": "OFF"}, {"name": "PKG_CONFIG_ARGN", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Arguments to supply to pkg-config"}], "type": "STRING", "value": ""}, {"name": "PKG_CONFIG_EXECUTABLE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "pkg-config executable"}], "type": "FILEPATH", "value": "/usr/local/bin/pkg-config"}, {"name": "PNG_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "PNG_LIBRARY_DEBUG-NOTFOUND"}, {"name": "PNG_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/local/lib/libpng.dylib"}, {"name": "PNG_PNG_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/usr/local/include"}, {"name": "SIZEOF_LONG", "properties": [{"name": "HELPSTRING", "value": "CHECK_TYPE_SIZE: sizeof(long)"}], "type": "INTERNAL", "value": "8"}, {"name": "TOOLCHAIN_SUPPORTS_ATTRIBUTE_CONSTRUCTOR", "properties": [{"name": "HELPSTRING", "value": "Test TOOLCHAIN_SUPPORTS_ATTRIBUTE_CONSTRUCTOR"}], "type": "INTERNAL", "value": "1"}, {"name": "TOOLCHAIN_SUPPORTS_ATTRIBUTE_CONSTRUCTOR_COMPILED", "properties": [{"name": "HELPSTRING", "value": "Result of TRY_COMPILE"}], "type": "INTERNAL", "value": "TRUE"}, {"name": "TOOLCHAIN_SUPPORTS_ATTRIBUTE_CONSTRUCTOR_EXITCODE", "properties": [{"name": "HELPSTRING", "value": "Result of try_run()"}], "type": "INTERNAL", "value": "0"}, {"name": "TOOLCHAIN_SUPPORTS_ATTRIBUTE_DESTRUCTOR", "properties": [{"name": "HELPSTRING", "value": "Test TOOLCHAIN_SUPPORTS_ATTRIBUTE_DESTRUCTOR"}], "type": "INTERNAL", "value": ""}, {"name": "TOOLCHAIN_SUPPORTS_ATTRIBUTE_DESTRUCTOR_COMPILED", "properties": [{"name": "HELPSTRING", "value": "Result of TRY_COMPILE"}], "type": "INTERNAL", "value": "TRUE"}, {"name": "TOOLCHAIN_SUPPORTS_ATTRIBUTE_DESTRUCTOR_EXITCODE", "properties": [{"name": "HELPSTRING", "value": "Result of try_run()"}], "type": "INTERNAL", "value": "1"}, {"name": "ZLIB_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a file."}], "type": "PATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include"}, {"name": "ZLIB_LIBRARY_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "ZLIB_LIBRARY_DEBUG-NOTFOUND"}, {"name": "ZLIB_LIBRARY_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd"}, {"name": "_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "CMAKE_INSTALL_PREFIX during last run"}], "type": "INTERNAL", "value": "/usr/local"}, {"name": "__pkg_config_arguments_GLIB2", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "glib-2.0"}, {"name": "__pkg_config_arguments_GTK3", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "gtk+-3.0"}, {"name": "__pkg_config_checked_GLIB2", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "__pkg_config_checked_GTK3", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "1"}, {"name": "pixman_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/work/pixman2/build"}, {"name": "pixman_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "ON"}, {"name": "pixman_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/work/pixman2"}, {"name": "pkgcfg_lib_GLIB2_glib-2.0", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/local/Cellar/glib/2.84.2/lib/libglib-2.0.dylib"}, {"name": "pkgcfg_lib_GLIB2_intl", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/local/opt/gettext/lib/libintl.dylib"}, {"name": "pkgcfg_lib_GTK3_atk-1.0", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/local/lib/libatk-1.0.dylib"}, {"name": "pkgcfg_lib_GTK3_cairo", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/local/lib/libcairo.a"}, {"name": "pkgcfg_lib_GTK3_cairo-gobject", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/local/lib/libcairo-gobject.dylib"}, {"name": "pkgcfg_lib_GTK3_dl", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libdl.tbd"}, {"name": "pkgcfg_lib_GTK3_fontconfig", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/local/lib/libfontconfig.dylib"}, {"name": "pkgcfg_lib_GTK3_freetype", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/local/lib/libfreetype.dylib"}, {"name": "pkgcfg_lib_GTK3_gdk-3", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/local/Cellar/gtk+3/3.24.43/lib/libgdk-3.dylib"}, {"name": "pkgcfg_lib_GTK3_gdk_pixbuf-2.0", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/local/lib/libgdk_pixbuf-2.0.dylib"}, {"name": "pkgcfg_lib_GTK3_gio-2.0", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/local/lib/libgio-2.0.dylib"}, {"name": "pkgcfg_lib_GTK3_glib-2.0", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/local/lib/libglib-2.0.dylib"}, {"name": "pkgcfg_lib_GTK3_gobject-2.0", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/local/lib/libgobject-2.0.dylib"}, {"name": "pkgcfg_lib_GTK3_gtk-3", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/local/Cellar/gtk+3/3.24.43/lib/libgtk-3.dylib"}, {"name": "pkgcfg_lib_GTK3_harfbuzz", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/local/Cellar/harfbuzz/11.1.0/lib/libharfbuzz.dylib"}, {"name": "pkgcfg_lib_GTK3_intl", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/local/lib/libintl.dylib"}, {"name": "pkgcfg_lib_GTK3_m", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd"}, {"name": "pkgcfg_lib_GTK3_pango-1.0", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/local/Cellar/pango/1.56.3/lib/libpango-1.0.dylib"}, {"name": "pkgcfg_lib_GTK3_pangocairo-1.0", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/local/Cellar/pango/1.56.3/lib/libpangocairo-1.0.dylib"}, {"name": "pkgcfg_lib_GTK3_pixman-1", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/local/lib/libpixman-1.dylib"}, {"name": "pkgcfg_lib_GTK3_png16", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/usr/local/lib/libpng16.dylib"}, {"name": "pkgcfg_lib_GTK3_z", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd"}, {"name": "prefix_result", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "INTERNAL", "value": "/usr/local/Cellar/glib/2.84.2/lib"}], "kind": "cache", "version": {"major": 2, "minor": 0}}