{"archive": {}, "artifacts": [{"path": "demos/libdemo.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_compile_definitions", "target_include_directories"], "files": ["demos/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 51, "parent": 0}, {"command": 1, "file": 0, "line": 63, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 97, "parent": 3}, {"command": 3, "file": 0, "line": 56, "parent": 0}, {"command": 3, "file": 0, "line": 74, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu99"}], "defines": [{"backtrace": 4, "define": "HAVE_CONFIG_H"}], "includes": [{"backtrace": 5, "path": "/Users/<USER>/work/pixman2/build"}, {"backtrace": 5, "path": "/Users/<USER>/work/pixman2/build/demos"}, {"backtrace": 6, "path": "/usr/local/Cellar/gtk+3/3.24.43/include/gtk-3.0"}, {"backtrace": 6, "path": "/usr/local/Cellar/pango/1.56.3/include/pango-1.0"}, {"backtrace": 6, "path": "/usr/local/Cellar/harfbuzz/11.1.0/include/harfbuzz"}, {"backtrace": 6, "path": "/usr/local/Cellar/graphite2/1.3.14/include"}, {"backtrace": 6, "path": "/usr/local/include/cairo"}, {"backtrace": 6, "path": "/usr/local/opt/freetype/include/freetype2"}, {"backtrace": 6, "path": "/usr/local/Cellar/pixman/0.46.0/include/pixman-1"}, {"backtrace": 6, "path": "/usr/local/include/gdk-pixbuf-2.0"}, {"backtrace": 6, "path": "/usr/local/opt/libpng/include/libpng16"}, {"backtrace": 6, "path": "/usr/local/opt/libtiff/include"}, {"backtrace": 6, "path": "/usr/local/opt/zstd/include"}, {"backtrace": 6, "path": "/usr/local/Cellar/xz/5.8.1/include"}, {"backtrace": 6, "path": "/usr/local/opt/jpeg-turbo/include"}, {"backtrace": 6, "path": "/usr/local/Cellar/at-spi2-core/2.56.1/include/atk-1.0"}, {"backtrace": 6, "path": "/usr/local/Cellar/libepoxy/1.5.10/include"}, {"backtrace": 6, "path": "/usr/local/Cellar/fribidi/1.0.16/include/fribidi"}, {"backtrace": 6, "path": "/usr/local/Cellar/glib/2.84.2/include/gio-unix-2.0"}, {"backtrace": 6, "path": "/usr/local/Cellar/glib/2.84.2/include"}, {"backtrace": 6, "path": "/Library/Developer/CommandLineTools/SDKs/MacOSX12.sdk/usr/include/ffi"}, {"backtrace": 6, "path": "/usr/local/Cellar/glib/2.84.2/include/glib-2.0"}, {"backtrace": 6, "path": "/usr/local/Cellar/glib/2.84.2/lib/glib-2.0/include"}, {"backtrace": 6, "path": "/usr/local/opt/gettext/include"}, {"backtrace": 6, "path": "/usr/local/Cellar/pcre2/10.45/include"}, {"backtrace": 2, "path": "/Users/<USER>/work/pixman2/pixman"}, {"backtrace": 2, "path": "/Users/<USER>/work/pixman2/build/pixman"}, {"backtrace": 2, "path": "/Users/<USER>/work/pixman2/test/utils"}], "language": "C", "languageStandard": {"backtraces": [1], "standard": "99"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 2, "id": "pixman-1::@d2e911910b37690d273b"}, {"backtrace": 2, "id": "testutils::@1e67207dbfbf1b9d4649"}], "id": "demo::@a9e843653e3a5da897f6", "name": "demo", "nameOnDisk": "libdemo.a", "paths": {"build": "demos", "source": "demos"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "demos/gtk-utils.c", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}