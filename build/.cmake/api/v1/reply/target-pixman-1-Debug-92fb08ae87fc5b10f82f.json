{"archive": {}, "artifacts": [{"path": "pixman/libpixman-1.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "add_compile_definitions", "target_include_directories"], "files": ["pixman/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 192, "parent": 0}, {"command": 1, "file": 0, "line": 232, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 97, "parent": 3}, {"command": 3, "file": 0, "line": 221, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu99"}], "defines": [{"backtrace": 4, "define": "HAVE_CONFIG_H"}], "includes": [{"backtrace": 5, "path": "/Users/<USER>/work/pixman2/pixman"}, {"backtrace": 5, "path": "/Users/<USER>/work/pixman2/build/pixman"}, {"backtrace": 5, "path": "/Users/<USER>/work/pixman2/build"}], "language": "C", "languageStandard": {"backtraces": [1], "standard": "99"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31]}], "id": "pixman-1::@d2e911910b37690d273b", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "/usr/local"}}, "name": "pixman-1", "nameOnDisk": "libpixman-1.a", "paths": {"build": "pixman", "source": "pixman"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [32, 33]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-access.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-access-accessors.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-arm.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-bits-image.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-combine32.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-combine-float.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-conical-gradient.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-edge.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-edge-accessors.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-fast-path.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-filter.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-glyph.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-general.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-gradient-walker.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-image.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-implementation.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-linear-gradient.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-matrix.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-mips.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-noop.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-ppc.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-radial-gradient.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-region16.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-region32.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-region64f.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-riscv.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-solid-fill.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-timer.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-trap.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-utils.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "pixman/pixman-x86.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "build/pixman/pixman-version.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "build/pixman-config.h", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}