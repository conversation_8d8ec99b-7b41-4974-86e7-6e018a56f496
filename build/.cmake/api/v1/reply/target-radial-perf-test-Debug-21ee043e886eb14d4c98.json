{"artifacts": [{"path": "test/radial-perf-test"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "set_target_properties", "find_package", "add_compile_definitions", "target_include_directories"], "files": ["test/CMakeLists.txt", "pixman/CMakeLists.txt", "test/utils/CMakeLists.txt", "/usr/local/share/cmake/Modules/FindPNG.cmake", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 108, "parent": 0}, {"command": 1, "file": 0, "line": 111, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 214, "parent": 3}, {"file": 2}, {"command": 1, "file": 2, "line": 51, "parent": 5}, {"file": 4}, {"command": 3, "file": 4, "line": 122, "parent": 7}, {"file": 3, "parent": 8}, {"command": 2, "file": 3, "line": 137, "parent": 9}, {"command": 4, "file": 4, "line": 97, "parent": 7}, {"command": 5, "file": 0, "line": 123, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu99"}], "defines": [{"backtrace": 11, "define": "HAVE_CONFIG_H"}], "includes": [{"backtrace": 12, "path": "/Users/<USER>/work/pixman2/build"}, {"backtrace": 2, "path": "/Users/<USER>/work/pixman2/pixman"}, {"backtrace": 2, "path": "/Users/<USER>/work/pixman2/build/pixman"}, {"backtrace": 2, "path": "/Users/<USER>/work/pixman2/test/utils"}], "language": "C", "languageStandard": {"backtraces": [1], "standard": "99"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 2, "id": "pixman-1::@d2e911910b37690d273b"}, {"backtrace": 2, "id": "testutils::@1e67207dbfbf1b9d4649"}], "id": "radial-perf-test::@36f028580bb02cc8272a", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"backtrace": 2, "fragment": "pixman/libpixman-1.a", "role": "libraries"}, {"backtrace": 2, "fragment": "test/utils/libtestutils.a", "role": "libraries"}, {"backtrace": 2, "fragment": "pixman/libpixman-1.a", "role": "libraries"}, {"backtrace": 4, "fragment": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libm.tbd", "role": "libraries"}, {"backtrace": 6, "fragment": "/usr/local/lib/libpng.dylib", "role": "libraries"}, {"backtrace": 10, "fragment": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libz.tbd", "role": "libraries"}], "language": "C"}, "name": "radial-perf-test", "nameOnDisk": "radial-perf-test", "paths": {"build": "test", "source": "test"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "test/radial-perf-test.c", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}