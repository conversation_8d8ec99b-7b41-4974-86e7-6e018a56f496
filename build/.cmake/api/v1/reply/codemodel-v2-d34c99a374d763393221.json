{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4], "hasInstallRule": true, "jsonFile": "directory-.-Debug-094862f942c6ab932b49.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": "."}, {"build": "pixman", "hasInstallRule": true, "jsonFile": "directory-pixman-Debug-7941ddb31866424a186b.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "pixman", "targetIndexes": [33]}, {"build": "test/utils", "jsonFile": "directory-test.utils-Debug-aa9932aedcc680a13641.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "test/utils", "targetIndexes": [53]}, {"build": "demos", "jsonFile": "directory-demos-Debug-bf75c1fb2a5e0148cc7f.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "demos", "targetIndexes": [4, 8, 9, 12, 14, 15, 17, 18, 24, 26, 37, 43, 48, 50, 51, 57, 58]}, {"build": "test", "jsonFile": "directory-test-Debug-451c0598f41488bb20b9.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "test", "targetIndexes": [0, 1, 2, 3, 5, 6, 7, 10, 11, 13, 16, 19, 20, 21, 22, 23, 25, 27, 28, 29, 30, 31, 32, 34, 35, 36, 38, 39, 40, 41, 42, 44, 45, 46, 47, 49, 52, 54, 55, 56]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4], "name": "pixman", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58]}], "targets": [{"directoryIndex": 4, "id": "a1-trap-test::@36f028580bb02cc8272a", "jsonFile": "target-a1-trap-test-Debug-be5ff5b17637bd44524e.json", "name": "a1-trap-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "affine-bench::@36f028580bb02cc8272a", "jsonFile": "target-affine-bench-Debug-af2c32efa99fecfab355.json", "name": "affine-bench", "projectIndex": 0}, {"directoryIndex": 4, "id": "affine-test::@36f028580bb02cc8272a", "jsonFile": "target-affine-test-Debug-56168f46f4222a198143.json", "name": "affine-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "alpha-loop::@36f028580bb02cc8272a", "jsonFile": "target-alpha-loop-Debug-be33191dece4005163a1.json", "name": "alpha-loop", "projectIndex": 0}, {"directoryIndex": 3, "id": "alpha-test::@a9e843653e3a5da897f6", "jsonFile": "target-alpha-test-Debug-59fd949ac8179a845ec1.json", "name": "alpha-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "alphamap::@36f028580bb02cc8272a", "jsonFile": "target-alphamap-Debug-5eadc61a821ca501fae9.json", "name": "alphamap", "projectIndex": 0}, {"directoryIndex": 4, "id": "blitters-test::@36f028580bb02cc8272a", "jsonFile": "target-blitters-test-Debug-d9a88f4ebc0ea18f27f8.json", "name": "blitters-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "check-formats::@36f028580bb02cc8272a", "jsonFile": "target-check-formats-Debug-7f62c897458c4635b61c.json", "name": "check-formats", "projectIndex": 0}, {"directoryIndex": 3, "id": "checkerboard::@a9e843653e3a5da897f6", "jsonFile": "target-checkerboard-Debug-923223437ce313e9fe6f.json", "name": "checkerboard", "projectIndex": 0}, {"directoryIndex": 3, "id": "clip-test::@a9e843653e3a5da897f6", "jsonFile": "target-clip-test-Debug-94bfd8a43b3391b79084.json", "name": "clip-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "combiner-test::@36f028580bb02cc8272a", "jsonFile": "target-combiner-test-Debug-3bb05963eadf443f57cc.json", "name": "combiner-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "composite::@36f028580bb02cc8272a", "jsonFile": "target-composite-Debug-9f8252e647240419e940.json", "name": "composite", "projectIndex": 0}, {"directoryIndex": 3, "id": "composite-test::@a9e843653e3a5da897f6", "jsonFile": "target-composite-test-Debug-02d79e2c7daa070e7a49.json", "name": "composite-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "composite-traps-test::@36f028580bb02cc8272a", "jsonFile": "target-composite-traps-test-Debug-705f27b356c6b4732a0a.json", "name": "composite-traps-test", "projectIndex": 0}, {"directoryIndex": 3, "id": "conical-test::@a9e843653e3a5da897f6", "jsonFile": "target-conical-test-Debug-305a2fb7dca5a7ef90b7.json", "name": "conical-test", "projectIndex": 0}, {"directoryIndex": 3, "id": "convolution-test::@a9e843653e3a5da897f6", "jsonFile": "target-convolution-test-Debug-8728b3b369816ef1805c.json", "name": "convolution-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "cover-test::@36f028580bb02cc8272a", "jsonFile": "target-cover-test-Debug-3b1564e02c7e6dce0ca3.json", "name": "cover-test", "projectIndex": 0}, {"directoryIndex": 3, "id": "demo::@a9e843653e3a5da897f6", "jsonFile": "target-demo-Debug-fe1faff4212334445706.json", "name": "demo", "projectIndex": 0}, {"directoryIndex": 3, "id": "dither::@a9e843653e3a5da897f6", "jsonFile": "target-dither-Debug-040413d1684cff6afa8c.json", "name": "dither", "projectIndex": 0}, {"directoryIndex": 4, "id": "fence-image-self-test::@36f028580bb02cc8272a", "jsonFile": "target-fence-image-self-test-Debug-22ec48de74802161d840.json", "name": "fence-image-self-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "fetch-test::@36f028580bb02cc8272a", "jsonFile": "target-fetch-test-Debug-680d2dbc7ec6f00273d8.json", "name": "fetch-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "filter-reduction-test::@36f028580bb02cc8272a", "jsonFile": "target-filter-reduction-test-Debug-dafe272338567465c8a5.json", "name": "filter-reduction-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "glyph-test::@36f028580bb02cc8272a", "jsonFile": "target-glyph-test-Debug-7e9ab162c9adb89529f2.json", "name": "glyph-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "gradient-crash-test::@36f028580bb02cc8272a", "jsonFile": "target-gradient-crash-test-Debug-f4c706fcc9df48ee1472.json", "name": "gradient-crash-test", "projectIndex": 0}, {"directoryIndex": 3, "id": "gradient-test::@a9e843653e3a5da897f6", "jsonFile": "target-gradient-test-Debug-8a18c6b196f8637f05c1.json", "name": "gradient-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "infinite-loop::@36f028580bb02cc8272a", "jsonFile": "target-infinite-loop-Debug-8557332f94c89d9ca582.json", "name": "infinite-loop", "projectIndex": 0}, {"directoryIndex": 3, "id": "linear-gradient::@a9e843653e3a5da897f6", "jsonFile": "target-linear-gradient-Debug-ff9f60a2af5b8ddb1bda.json", "name": "linear-gradient", "projectIndex": 0}, {"directoryIndex": 4, "id": "lowlevel-blt-bench::@36f028580bb02cc8272a", "jsonFile": "target-lowlevel-blt-bench-Debug-3c4f6cf7d60c5088fde7.json", "name": "lowlevel-blt-bench", "projectIndex": 0}, {"directoryIndex": 4, "id": "matrix-test::@36f028580bb02cc8272a", "jsonFile": "target-matrix-test-Debug-db74e7721182fe37ada1.json", "name": "matrix-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "neg-stride-test::@36f028580bb02cc8272a", "jsonFile": "target-neg-stride-test-Debug-f402d5160621861ed700.json", "name": "neg-stride-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "oob-test::@36f028580bb02cc8272a", "jsonFile": "target-oob-test-Debug-6a08189a6b0898d3981a.json", "name": "oob-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "pdf-op-test::@36f028580bb02cc8272a", "jsonFile": "target-pdf-op-test-Debug-3df4ae002dd98bb5acd0.json", "name": "pdf-op-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "pixel-test::@36f028580bb02cc8272a", "jsonFile": "target-pixel-test-Debug-c5c47d06f2ed1dc3c6e8.json", "name": "pixel-test", "projectIndex": 0}, {"directoryIndex": 1, "id": "pixman-1::@d2e911910b37690d273b", "jsonFile": "target-pixman-1-Debug-92fb08ae87fc5b10f82f.json", "name": "pixman-1", "projectIndex": 0}, {"directoryIndex": 4, "id": "prng-test::@36f028580bb02cc8272a", "jsonFile": "target-prng-test-Debug-01f2228ca6d8abe0322c.json", "name": "prng-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "radial-invalid::@36f028580bb02cc8272a", "jsonFile": "target-radial-invalid-Debug-85b66020b52d20c9fd27.json", "name": "radial-invalid", "projectIndex": 0}, {"directoryIndex": 4, "id": "radial-perf-test::@36f028580bb02cc8272a", "jsonFile": "target-radial-perf-test-Debug-21ee043e886eb14d4c98.json", "name": "radial-perf-test", "projectIndex": 0}, {"directoryIndex": 3, "id": "radial-test::@a9e843653e3a5da897f6", "jsonFile": "target-radial-test-Debug-281a913a26c181aef397.json", "name": "radial-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "region-contains-test::@36f028580bb02cc8272a", "jsonFile": "target-region-contains-test-Debug-ceda902946b117d68998.json", "name": "region-contains-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "region-fractional-test::@36f028580bb02cc8272a", "jsonFile": "target-region-fractional-test-Debug-23883e8587fc312eb4d9.json", "name": "region-fractional-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "region-test::@36f028580bb02cc8272a", "jsonFile": "target-region-test-Debug-9cfceea6f85203b772e1.json", "name": "region-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "region-translate-test::@36f028580bb02cc8272a", "jsonFile": "target-region-translate-test-Debug-4b98e977ba08998c3931.json", "name": "region-translate-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "rotate-test::@36f028580bb02cc8272a", "jsonFile": "target-rotate-test-Debug-d2c45cfa240b57dab21f.json", "name": "rotate-test", "projectIndex": 0}, {"directoryIndex": 3, "id": "scale::@a9e843653e3a5da897f6", "jsonFile": "target-scale-Debug-6a6d480b9178e38757e6.json", "name": "scale", "projectIndex": 0}, {"directoryIndex": 4, "id": "scaling-bench::@36f028580bb02cc8272a", "jsonFile": "target-scaling-bench-Debug-42d54b53df1284d5b91b.json", "name": "scaling-bench", "projectIndex": 0}, {"directoryIndex": 4, "id": "scaling-crash-test::@36f028580bb02cc8272a", "jsonFile": "target-scaling-crash-test-Debug-68204f54df0abdd8e808.json", "name": "scaling-crash-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "scaling-helpers-test::@36f028580bb02cc8272a", "jsonFile": "target-scaling-helpers-test-Debug-3736166c4cc80c6b3ea8.json", "name": "scaling-helpers-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "scaling-test::@36f028580bb02cc8272a", "jsonFile": "target-scaling-test-Debug-54f36fa997a423dcb990.json", "name": "scaling-test", "projectIndex": 0}, {"directoryIndex": 3, "id": "screen-test::@a9e843653e3a5da897f6", "jsonFile": "target-screen-test-Debug-8e88869c35564813e5cc.json", "name": "screen-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "solid-test::@36f028580bb02cc8272a", "jsonFile": "target-solid-test-Debug-f5d0e233296d6a67406c.json", "name": "solid-test", "projectIndex": 0}, {"directoryIndex": 3, "id": "srgb-test::@a9e843653e3a5da897f6", "jsonFile": "target-srgb-test-Debug-a71320b3b44036dc1df6.json", "name": "srgb-test", "projectIndex": 0}, {"directoryIndex": 3, "id": "srgb-trap-test::@a9e843653e3a5da897f6", "jsonFile": "target-srgb-trap-test-Debug-aca0cfd2f16be26a168f.json", "name": "srgb-trap-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "stress-test::@36f028580bb02cc8272a", "jsonFile": "target-stress-test-Debug-145d76fd5d268c78afb9.json", "name": "stress-test", "projectIndex": 0}, {"directoryIndex": 2, "id": "testutils::@1e67207dbfbf1b9d4649", "jsonFile": "target-testutils-Debug-914e9c9ab3a7536e1d3b.json", "name": "testutils", "projectIndex": 0}, {"directoryIndex": 4, "id": "thread-test::@36f028580bb02cc8272a", "jsonFile": "target-thread-test-Debug-2cf2dcf3bb85e41fe084.json", "name": "thread-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "tolerance-test::@36f028580bb02cc8272a", "jsonFile": "target-tolerance-test-Debug-7486706e27d868373a99.json", "name": "tolerance-test", "projectIndex": 0}, {"directoryIndex": 4, "id": "trap-crasher::@36f028580bb02cc8272a", "jsonFile": "target-trap-crasher-Debug-bc86f584ec7caa1a25e1.json", "name": "trap-crasher", "projectIndex": 0}, {"directoryIndex": 3, "id": "trap-test::@a9e843653e3a5da897f6", "jsonFile": "target-trap-test-Debug-d636de9deb80332ffbff.json", "name": "trap-test", "projectIndex": 0}, {"directoryIndex": 3, "id": "tri-test::@a9e843653e3a5da897f6", "jsonFile": "target-tri-test-Debug-bdd4c5f8743b14014be3.json", "name": "tri-test", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/work/pixman2/build", "source": "/Users/<USER>/work/pixman2"}, "version": {"major": 2, "minor": 8}}