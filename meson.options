# Copyright © 2018 Intel Corporation

# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:

# The above copyright notice and this permission notice shall be included in
# all copies or substantial portions of the Software.

# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
# SOFTWARE.

option(
  'loongson-mmi',
  type : 'feature',
  description : 'Use Loongson M<PERSON> intrinsic optimized paths',
)
option(
  'mmx',
  type : 'feature',
  description : 'Use X86 MMX intrinsic optimized paths',
)
option(
  'sse2',
  type : 'feature',
  description : 'Use X86 SSE2 intrinsic optimized paths',
)
option(
  'ssse3',
  type : 'feature',
  description : 'Use X86 SSSE3 intrinsic optimized paths',
)
option(
  'vmx',
  type : 'feature',
  description : 'Use PPC VMX/Altivec intrinsic optimized paths',
)
option(
  'arm-simd',
  type : 'feature',
  description : 'Use ARMv6 SIMD intrinsic optimized paths',
)
option(
  'neon',
  type : 'feature',
  description : 'Use ARM NEON intrinsic optimized paths',
)
option(
  'a64-neon',
  type : 'feature',
  description : 'Use ARM A64 NEON intrinsic optimized paths',
)
option(
  'mips-dspr2',
  type : 'feature',
  description : 'Use MIPS32 DSPr2 intrinsic optimized paths',
)
option(
  'rvv',
  type : 'feature',
  description : 'Use RISC-V Vector extension',
)
option(
  'gnu-inline-asm',
  type : 'feature',
  description : 'Use GNU style inline assembler',
)
option(
  'tls',
  type : 'feature',
  description : 'Use compiler support for thread-local storage',
)
option(
  'cpu-features-path',
  type : 'string',
  description : 'Path to platform-specific cpu-features.[ch] for systems that do not provide it (e.g. Android)',
)
option(
  'openmp',
  type : 'feature',
  description : 'Enable OpenMP for tests',
)
option(
  'timers',
  type : 'boolean',
  value : false,
  description : 'Enable TIMER_* macros',
)
option(
  'gnuplot',
  type : 'boolean',
  value : false,
  description : 'Enable output of filters that can be piped to gnuplot',
)
option(
  'gtk',
  type : 'feature',
  description : 'Enable demos using GTK',
)
option(
  'libpng',
  type : 'feature',
  description : 'Use libpng in tests'
)
option(
  'tests',
  type : 'feature',
  description : 'Build tests'
)
option(
  'demos',
  type : 'feature',
  description : 'Build demos'
)
