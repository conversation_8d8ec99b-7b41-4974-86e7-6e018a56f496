#include <math.h>
#include <stdio.h>
#include <pixman.h>

/* This code is basically the output of Maxima translated into C.
 *
 * See http://maxima.sourceforge.net/
 */
static void
quad_to_quad (double x0, double y0,
	      double x1, double y1,
	      double x2, double y2,
	      double x3, double y3,

	      double px0, double py0,
	      double px1, double py1,
	      double px2, double py2,
	      double px3, double py3,

	      struct pixman_f_transform *trans)
{
    double
	t1, t2, t3, t4, t5, t6, t7, t8, t9, t10, t11, t12, t13, t14, t15, t16, t17, t18,
	t19, t20, t21, t22, t23, t24, t25, t26, t27, t28, t29, t30, t31, t32, t33, t34,
	t35, t36, t37, t38, t39, t40, t41, t42, t43, t44, t45, t46, t47, t48, t49, t50,
	t51, t52, t53, t54, t55, t56, t57, t58, t59, t60, t61, t62, t63, t64, t65, t66,
	t67, t68, t69, t70, t71, t72, t73, t74, t75, t76, t77, t78, t79, t80, t81, t82,
	t83, t84, t85, t86, t87, t88, t89, t90, t91, t92, t93, t94, t95, t96, t97, t98,
	t99, t100, t101, t102, t103, t104, t105, t106, t107, t108, t109, t110, t111,
	t112, t113, t114, t115, t116, t117, t118, t119, t120, t121, t122, t123,
	t124, t125, t126, t127, t128, t129, t130, t131, t132, t133, t134, t135,
	t136, t137, t138, t139, t140, t141, t142, t143, t144, t145, t146, t147,
	t148, t149, t150, t151, t152, t153, t154, t155, t156, t157, t158, t159,
	t160, t161, t162, t163, t164, t165, t166, t167, t168, t169, t170, t171,
	t172, t173, t174, t175, t176, t177, t178, t179, t180, t181, t182, t183,
	t184, t185, t186, t187, t188, t189, t190, t191, t192, t193, t194, t195,
	t196, t197, t198, t199, t200, t201, t202, t203, t204, t205, t206, t207,
	t208, t209, t210, t211, t212, t213, t214, t215, t216, t217, t218, t219,
	t220, t221, t222, t223, t224, t225, t226, t227, t228, t229, t230, t231,
	t232, t233, t234, t235, t236, t237, t238, t239, t240, t241, t242, t243,
	t244, t245, t246, t247, t248, t249, t250, t251, t252, t253, t254, t255,
	t256, t257, t258, t259, t260, t261, t262, t263, t264, t265, t266, t267,
	t268, t269, t270, t271, t272, t273, t274, t275, t276, t277, t278, t279,
	t280, t281, t282, t283, t284, t285, t286, t287, t288, t289, t290, t291,
	t292, t293, t294, t295, t296, t297, t298, t299, t300, t301, t302, t303,
	t304, t305, t306, t307, t308, t309, t310, t311, t312, t313, t314, t315,
	t316, t317, t318, t319, t320, t321, t322, t323, t324, t325, t326, t327,
	t328, t329, t330, t331, t332, t333, t334, t335, t336, t337, t338, t339,
	t340, t341, t342, t343, t344, t345, t346, t347, t348, t349, t350, t351,
	t352, t353, t354, t355, t356, t357, t358, t359, t360, t361, t362, t363,
	t364, t365, t366, t367, t368, t369, t370, t371, t372, t373, t374, t375,
	t376, t377, t378, t379, t380, t381, t382, t383, t384, t385, t386, t387,
	t388, t389, t390, t391, t392, t393, t394, t395, t396, t397, t398, t399,
	t400, t401, t402, t403, t404, t405, t406, t407, t408, t409, t410, t411,
	t412, t413, t414, t415, t416, t417, t418, t419, t420, t421, t422, t423,
	t424, t425, t426, t427, t428, t429, t430, t431, t432, t433, t434, t435,
	t436, t437, t438, t439, t440, t441, t442, t443, t444, t445, t446, t447,
	t448, t449, t450, t451, t452, t453, t454, t455, t456, t457, t458, t459,
	t460, t461, t462, t463, t464, t465, t466, t467, t468, t469, t470, t471,
	t472, t473, t474, t475, t476, t477, t478, t479, t480, t481, t482, t483,
	t484, t485, t486, t487, t488, t489, t490, t491, t492, t493, t494, t495,
	t496, t497, t498, t499, t500, t501, t502, t503, t504, t505, t506, t507,
	t508, t509, t510, t511, t512, t513, t514, t515, t516, t517, t518, t519,
	t520, t521, t522, t523, t524, t525, t526, t527, t528, t529, t530, t531,
	t532, t533, t534, t535, t536, t537, t538, t539, t540, t541, t542, t543,
	t544, t545, t546, t547, t548, t549, t550, t551, t552, t553, t554, t555,
	t556, t557, t558, t559, t560, t561, t562, t563, t564, t565, t566, t567,
	t568, t569, t570, t571, t572, t573, t574, t575, t576, t577, t578, t579,
	t580, t581, t582, t583, t584, t585, t586, t587, t588, t589, t590, t591,
	t592, t593, t594, t595, t596, t597, t598, t599, t600, t601, t602, t603,
	t604, t605, t606, t607, t608, t609, t610, t611, t612, t613, t614, t615,
	t616, t617, t618, t619, t620, t621, t622, t623, t624, t625, t626, t627,
	t628, t629, t630, t631, t632, t633, t634, t635, t636, t637, t638, t639,
	t640, t641, t642, t643, t644, t645, t646, t647, t648, t649, t650, t651,
	t652, t653, t654, t655, t656, t657, t658, t659, t660, t661, t662, t663,
	t664, t665, t666, t667, t668, t669, t670, t671, t672, t673, t674, t675,
	t676, t677, t678, t679, t680, t681, t682, t683, t684, t685, t686, t687,
	t688, t689, t690, t691, t692, t693, t694, t695, t696, t697, t698, t699,
	t700, t701, t702, t703, t704, t705, t706, t707, t708, t709, t710, t711,
	t712, t713, t714, t715, t716, t717, t718, t719, t720, t721, t722, t723,
	t724, t725, t726, t727, t728, t729, t730, t731, t732, t733, t734, t735,
	t736, t737, t738, t739, t740, t741, t742, t743, t744, t745, t746, t747,
	t748, t749, t750, t751, t752, t753, t754, t755, t756, t757, t758, t759,
	t760, t761, t762, t763, t764, t765, t766, t767, t768, t769, t770, t771,
	t772, t773, t774, t775, t776, t777, t778, t779, t780, t781, t782, t783,
	t784, t785, t786, t787, t788, t789, t790, t791, t792, t793, t794, t795,
	t796, t797, t798, t799, t800, t801, t802, t803, t804, t805, t806, t807,
	t808, t809, t810, t811, t812, t813, t814, t815, t816, t817, t818, t819,
	t820, t821, t822, t823, t824, t825, t826, t827, t828, t829, t830, t831,
	t832, t833, t834, t835, t836, t837, t838, t839, t840, t841, t842, t843,
	t844, t845, t846, t847, t848, t849, t850, t851, t852, t853, t854, t855,
	t856, t857, t858, t859, t860, t861, t862, t863, t864, t865, t866, t867,
	t868, t869, t870, t871, t872, t873, t874, t875, t876, t877, t878, t879,
	t880, t881, t882, t883, t884, t885, t886, t887, t888, t889, t890, t891,
	t892, t893, t894, t895, t896, t897, t898, t899, t900, t901, t902, t903,
	t904, t905, t906, t907, t908, t909, t910, t911, t912, t913, t914, t915,
	t916, t917, t918, t919, t920, t921, t922, t923, t924, t925, t926, t927,
	t928, t929, t930, t931, t932, t933, t934, t935, t936, t937, t938, t939,
	t940, t941, t942, t943, t944, t945, t946, t947, t948, t949, t950, t951,
	t952, t953, t954, t955, t956, t957, t958, t959, t960, t961, t962, t963,
	t964, t965, t966, t967, t968, t969, t970, t971, t972, t973, t974, t975,
	t976, t977, t978, t979, t980, t981, t982, t983, t984, t985, t986, t987,
	t988, t989, t990, t991, t992, t993, t994, t995, t996, t997, t998, t999,
	t1000, t1001, t1002, t1003, t1004, t1005, t1006, t1007, t1008, t1009,
	t1010, t1011, t1012, t1013, t1014, t1015, t1016, t1017, t1018, t1019,
	t1020, t1021, t1022, t1023, t1024, t1025, t1026, t1027, t1028, t1029,
	t1030, t1031, t1032, t1033, t1034, t1035, t1036, t1037, t1038, t1039,
	t1040, t1041, t1042, t1043, t1044, t1045, t1046, t1047, t1048, t1049,
	t1050, t1051, t1052, t1053, t1054, t1055, t1056, t1057, t1058, t1059,
	t1060, t1061, t1062, t1063, t1064, t1065, t1066, t1067, t1068, t1069,
	t1070, t1071, t1072, t1073;

    t1 = y1 * y1;
    t2 = x3 * x3;
    t3 = px2 * px3 * t2;
    t4 = (t3 - px2 * px3 * x2 * x3) * y2;
    t5 = x2 * x2;
    t6 = px2 * px3 * t5 * y3;

    t7 = - px2 * px3 * x2 * x3 * y3;
    t8 = py1 * (t7 + t6 + t4);
    t9 = px3 * py2 * x2 * x3;

    t10 = - px3 * py2 * t2;
    t11 = (t10 + t9) * y2;
    t12 = - px2 * py3 * t5 * y3;

    t13 = px2 * py3 * x2 * x3 * y3;
    t14 = y0 * y0;
    t15 = - px3 * py2;
    t16 = px2 * py3;

    t17 = t16 + t15;
    t18 = t17 * x2;
    t19 = px3 * py2 * x3;
    t20 = - px2 * py3 * x3;

    t21 = t20 + t19 + t18;
    t22 = px2 * px3 * t5;
    t23 = - 2 * px2 * px3 * x2 * x3;

    t24 = py1 * (t3 + t23 + t22);
    t25 = - px2 * py3 * t5;
    t26 = px2 * py3 * x3;

    t27 = x2 * (t26 + t19);
    t28 = t10 + t27 + t25;
    t29 = x1 * x1;
    t30 = px3 * py2;

    t31 = - px2 * py3;
    t32 = t31 + t30;
    t33 = t32 * y2;
    t34 = - px3 * py2 * y3;

    t35 = px2 * py3 * y3;
    t36 = t35 + t34 + t33;
    t37 = - px2 * px3 * t2;

    t38 = (t37 + px2 * px3 * x2 * x3) * y2;
    t39 = - px2 * px3 * t5 * y3;

    t40 = px2 * px3 * x2 * x3 * y3;
    t41 = py1 * (t40 + t39 + t38);
    t42 = - px2 * py3 * x2 * x3;

    t43 = px3 * py2 * t2;
    t44 = (t43 + t42) * y2;
    t45 = px2 * py3 * t5 * y3;

    t46 = - px3 * py2 * x2 * x3 * y3;
    t47 = (px2 * px3 * x3 - px2 * px3 * x2) * y2;

    t48 = px2 * px3 * x2 * y3;
    t49 = - px2 * px3 * x3 * y3;
    t50 = py1 * (t49 + t48 + t47);

    t51 = px2 * py3 * x2;
    t52 = - 2 * px3 * py2 * x3;
    t53 = (t26 + t52 + t51) * y2;

    t54 = px3 * py2 * x3 * y3;
    t55 = px3 * py2 * y3;
    t56 = - 2 * px2 * py3 * y3;
    t57 = t56 + t55;

    t58 = x2 * t57;
    t59 = - px2 * px3 * t5;
    t60 = 2 * px2 * px3 * x2 * x3;
    t61 = - px2;

    t62 = px3 + t61;
    t63 = t62 * x2;
    t64 = px2 * x3;
    t65 = - px3 * x3;
    t66 = t65 + t64 + t63;

    t67 = px2 * t5;
    t68 = - px2 * x3;
    t69 = x2 * (t65 + t68);
    t70 = px3 * t2;

    t71 = t70 + t69 + t67;
    t72 = - px3;
    t73 = t72 + px2;
    t74 = - px2 * y3;
    t75 = px3 * y3;

    t76 = t75 + t74 + t73 * y2;
    t77 = px2 * x2 * x3;
    t78 = - px3 * t2;
    t79 = - px2 * t5 * y3;

    t80 = px3 * x2 * x3 * y3;
    t81 = t80 + t79 + (t78 + t77) * y2;

    t82 = (px2 * px3 * x2 - px2 * px3 * x3) * y2;
    t83 = - px2 * px3 * x2 * y3;

    t84 = px2 * px3 * x3 * y3;
    t85 = - px2 * x2;
    t86 = 2 * px3 * x3;
    t87 = - px3 * x3 * y3;

    t88 = 2 * px2 * y3;
    t89 = - px3 * y3;
    t90 = t89 + t88;
    t91 = x2 * t90;

    t92 = t91 + t87 + (t86 + t68 + t85) * y2;
    t93 = px2 * py3 * t5;
    t94 = - px3 * py2 * x3;

    t95 = x2 * (t20 + t94);
    t96 = t32 * x2;
    t97 = t73 * x2;
    t98 = px3 * x3;

    t99 = t98 + t68 + t97;
    t100 = py1 * t99;
    t101 = - px2 * t5;
    t102 = x2 * (t98 + t64);

    t103 = t78 + t102 + t101;
    t104 = py1 * t103;
    t105 = - py2;
    t106 = py3 + t105;

    t107 = py2 * y3;
    t108 = - py3 * y3;
    t109 = t108 + t107 + t106 * y2;
    t110 = - px3 * x2 * x3;

    t111 = px2 * t5 * y3;
    t112 = - px2 * x2 * x3 * y3;
    t113 = t112 + t111 + (t70 + t110) * y2;

    t114 = - py2 * x3;
    t115 = py3 * x3;
    t116 = t115 + t114;
    t117 = py2 * x3 * y3;

    t118 = - py3 * x3 * y3;
    t119 = t118 + t117;
    t120 = x2 * t119;

    t121 = px1 * (t120 + x2 * t116 * y2);
    t122 = - px3 * py2 * x2;
    t123 = (t19 + t122) * y2;

    t124 = px2 * py3 * x2 * y3;
    t125 = - px2 * py3 * x3 * y3;
    t126 = px3 * x2;

    t127 = - px2 * x2 * y3;
    t128 = px2 * x3 * y3;
    t129 = t128 + t127 + (t65 + t126) * y2;

    t130 = - py3;
    t131 = t130 + py2;
    t132 = t131 * x2;
    t133 = py2 * x3;
    t134 = - py3 * x3;

    t135 = - py2 * x3 * y3;
    t136 = py3 * x3 * y3;
    t137 = - py2 * y3;
    t138 = py3 * y3;

    t139 = t138 + t137;
    t140 = x2 * t139;

    t141 = px1 * (t140 + t136 + t135 + (t134 + t133 + t132) * y2);
    t142 = y2 * y2;

    t143 = - px3 * py2 * x3 * y3;
    t144 = px2 * py3 * x3 * y3;
    t145 = t144 + t143;

    t146 = t142 * t145;
    t147 = y3 * y3;
    t148 = px3 * py2 * t147;
    t149 = - px2 * py3 * t147;

    t150 = t149 + t148;
    t151 = x2 * y2 * t150;
    t152 = t151 + t146;
    t153 = - px2 * py3 * y3;

    t154 = t153 + t55;
    t155 = t142 * t154;
    t156 = - px3 * py2 * t147;

    t157 = px2 * py3 * t147;
    t158 = t157 + t156;
    t159 = y2 * t158;
    t160 = t159 + t155;

    t161 = x0 * x0;
    t162 = py1 * t76;
    t163 = px1 * t109;
    t164 = px2 * y3;
    t165 = t89 + t164;

    t166 = - px2 * t147;
    t167 = px3 * t147;
    t168 = t167 + t166;

    t169 = y2 * t168 + t142 * t165;
    t170 = py1 * t169;
    t171 = py2 * t147;

    t172 = - py3 * t147;
    t173 = t172 + t171;
    t174 = y2 * t173 + t142 * t139;

    t175 = px1 * t174;
    t176 = t17 * t142;
    t177 = px2 * t147;
    t178 = - px3 * t147;

    t179 = t178 + t177 + t62 * t142;
    t180 = - py2 * t147;
    t181 = py3 * t147;

    t182 = t181 + t180 + t131 * t142;

    t183 = y1 * (px1 * t182 + py1 * t179 + t149 + t148 + t176)
	+ t175 + t170 + t159 + t1 * (t163 + t162 + t35 + t34 + t33) + t155;

    t184 = - px2 * px3 * t2 * t142;
    t185 = 2 * px2 * px3 * x2 * x3 * y2 * y3;

    t186 = - px2 * px3 * t5 * t147;
    t187 = py1 * (t186 + t185 + t184);

    t188 = px3 * py2 * t2 * t142;
    t189 = x2 * y2 * (t125 + t143);
    t190 = px2 * py3 * t5 * t147;

    t191 = t190 + t189 + t188;
    t192 = px2 * px3 * x3 * t142;
    t193 = y2 * (t49 + t83);

    t194 = px2 * px3 * x2 * t147;
    t195 = py1 * (t194 + t193 + t192);

    t196 = - px3 * py2 * x3 * t142;
    t197 = 2 * px3 * py2 * x3 * y3;
    t198 = 2 * px2 * py3 * y3;

    t199 = t198 + t34;
    t200 = x2 * t199;
    t201 = y2 * (t200 + t125 + t197);

    t202 = - px2 * py3 * x2 * t147;
    t203 = - px2 * x3 * y3;
    t204 = px3 * x3 * y3;

    t205 = t204 + t203;
    t206 = t142 * t205;
    t207 = t178 + t177;
    t208 = x2 * y2 * t207;

    t209 = t208 + t206;
    t210 = px2 * px3 * t2 * t142;
    t211 = - 2 * px2 * px3 * x2 * x3 * y2 * y3;

    t212 = px2 * px3 * t5 * t147;
    t213 = - px3 * t2 * t142;
    t214 = x2 * y2 * (t204 + t128);

    t215 = - px2 * t5 * t147;
    t216 = t215 + t214 + t213;
    t217 = - px2 * px3 * x3 * t142;

    t218 = y2 * (t84 + t48);
    t219 = - px2 * px3 * x2 * t147;
    t220 = px3 * x3 * t142;

    t221 = - 2 * px3 * x3 * y3;
    t222 = - 2 * px2 * y3;
    t223 = t75 + t222;
    t224 = x2 * t223;

    t225 = y2 * (t224 + t221 + t128);
    t226 = px2 * x2 * t147;
    t227 = t226 + t225 + t220;

    t228 = t125 + t54;
    t229 = t142 * t228;
    t230 = x2 * y2 * t158;
    t231 = t87 + t128;

    t232 = t142 * t231;
    t233 = x2 * y2 * t168;
    t234 = t233 + t232;
    t235 = py1 * t234;

    t236 = - px3 * py2 * t2 * t142;
    t237 = x2 * y2 * (t144 + t54);

    t238 = - px2 * py3 * t5 * t147;
    t239 = px3 * t2 * t142;
    t240 = x2 * y2 * (t87 + t203);

    t241 = px2 * t5 * t147;
    t242 = t241 + t240 + t239;
    t243 = py1 * t242;

    t244 = px2 * py3 * x3 * t142;
    t245 = - px2 * py3 * x2 * y3;
    t246 = y2 * (t143 + t245);

    t247 = px3 * py2 * x2 * t147;
    t248 = - px2 * x3 * t142;
    t249 = px2 * x2 * y3;

    t250 = y2 * (t204 + t249);
    t251 = - px3 * x2 * t147;
    t252 = t251 + t250 + t248;

    t253 = t134 + t133;
    t254 = t253 * t142;
    t255 = t108 + t107;
    t256 = x2 * t255;

    t257 = t256 + t136 + t135;
    t258 = y2 * t257;
    t259 = t181 + t180;
    t260 = x2 * t259;

    t261 = px1 * (t260 + t258 + t254);
    t262 = py1 * (t37 + t60 + t59);

    t263 = t43 + t95 + t93;
    t264 = px1 * t263;
    t265 = t26 + t94;
    t266 = x2 * t265 * y2;

    t267 = x2 * t228;
    t268 = t267 + t266;
    t269 = py1 * (t84 + t83 + t82);

    t270 = - 2 * px2 * py3;
    t271 = (t26 + (t270 + t30) * x2) * y2;
    t272 = px3 * py2 * x2 * y3;

    t273 = - 2 * px3 * py2 * x3 * y3;
    t274 = t149 + t148 + t176;

    t275 = py1 * (t212 + t211 + t210);
    t276 = t238 + t237 + t236;
    t277 = px1 * t276;

    t278 = py1 * (t219 + t218 + t217);
    t279 = 2 * px3 * py2 * x3;
    t280 = t20 + t279;

    t281 = t280 * t142;
    t282 = - px3 * py2 * x2 * y3;
    t283 = y2 * (t125 + t282);

    t284 = 2 * px2 * py3 * t147;
    t285 = x2 * (t284 + t156);
    t286 = px1 * t103;

    t287 = t98 + t68;
    t288 = x2 * t287 * y2;
    t289 = x2 * t231;
    t290 = t289 + t288;

    t291 = 2 * px2;
    t292 = - px3 * x2 * y3;
    t293 = 2 * px3 * x3 * y3;

    t294 = t293 + t203 + t292 + (t68 + (t72 + t291) * x2) * y2;
    t295 = px1 * t242;

    t296 = - 2 * px3 * x3;
    t297 = t296 + t64;
    t298 = px3 * x2 * y3;
    t299 = y2 * (t128 + t298);

    t300 = - 2 * px2 * t147;
    t301 = x2 * (t167 + t300) + t299 + t297 * t142;
    t302 = py1 * t71;

    t303 = py1 * t290;
    t304 = 2 * py2 * x3;
    t305 = - 2 * py3 * x3;
    t306 = - 2 * py2 * x3 * y3;

    t307 = 2 * py3 * x3 * y3;
    t308 = t307 + t306;
    t309 = - 2 * px2 * py3 * x3;

    t310 = (t309 + t19 + t51) * y2;
    t311 = - 2 * px3 * py2 * y3;
    t312 = t35 + t311;

    t313 = x2 * t312;
    t314 = 2 * px2 * x3;
    t315 = 2 * px3 * y3;
    t316 = t315 + t74;

    t317 = x2 * t316;
    t318 = t317 + t87 + (t65 + t314 + t85) * y2;
    t319 = t106 * x2;

    t320 = px1 * (t256 + t118 + t117 + (t115 + t114 + t319) * y2);
    t321 = py1 * t216;

    t322 = 2 * px2 * py3 * x3 * y3;
    t323 = 2 * px3 * py2 * y3;
    t324 = t153 + t323;

    t325 = x2 * t324;
    t326 = y2 * (t325 + t322 + t143);
    t327 = - 2 * px2 * x3 * y3;

    t328 = - 2 * px3 * y3;
    t329 = t328 + t164;
    t330 = x2 * t329;

    t331 = y2 * (t330 + t204 + t327);
    t332 = t226 + t331 + t220;
    t333 = t116 * t142;

    t334 = t140 + t118 + t117;
    t335 = y2 * t334;
    t336 = x2 * t173;

    t337 = px1 * (t336 + t335 + t333);
    t338 = t26 + t94 + t96;
    t339 = t17 * y2;

    t340 = t153 + t55 + t339;
    t341 = px2 * px3 * t142;
    t342 = - 2 * px2 * px3 * y2 * y3;

    t343 = px2 * px3 * t147;
    t344 = py1 * (t343 + t342 + t341);
    t345 = - px2 * py3 * t142;

    t346 = y2 * (t35 + t55);
    t347 = t156 + t346 + t345;
    t348 = px1 * t347 + t344;

    t349 = t89 + t164 + t62 * y2;
    t350 = - px2 * px3 * t142;
    t351 = 2 * px2 * px3 * y2 * y3;

    t352 = - px2 * px3 * t147;
    t353 = px2 * t142;
    t354 = y2 * (t89 + t74);

    t355 = t167 + t354 + t353;
    t356 = px1 * t355 + t352 + t351 + t350;
    t357 = py1 * t66;

    t358 = py1 * t349;
    t359 = 2 * py2;
    t360 = - 2 * py3;
    t361 = - 2 * py2 * y3;

    t362 = 2 * py3 * y3;
    t363 = px3 * py2 * t142;
    t364 = y2 * (t153 + t34);

    t365 = - px3 * t142;
    t366 = y2 * (t75 + t164);
    t367 = t166 + t366 + t365;

    t368 = py1 * t367;
    t369 = px1 * (t172 + t171 + t106 * t142);
    t370 = t35 + t34;

    t371 = t142 * t370;
    t372 = y2 * t150;
    t373 = t372 + t371;
    t374 = t230 + t229;

    t375 = py1 * (t352 + t351 + t350);
    t376 = t157 + t364 + t363;
    t377 = px1 * t376 + t375;

    t378 = t75 + t74;
    t379 = y2 * t207 + t142 * t378;
    t380 = px1 * t367 + t343 + t342 + t341;

    t381 = py1 * t209;
    t382 = py1 * t355;
    t383 = py1 * t379;
    t384 = 2 * py2 * y3;

    t385 = - 2 * py3 * y3;
    t386 = t385 + t384;
    t387 = - 2 * py2 * t147;
    t388 = 2 * py3 * t147;

    t389 = px2 * py3 * t2;
    t390 = t389 + t10;
    t391 = x2 * t390 * y2;
    t392 = t5 * t228;

    t393 = - px2 * t2;
    t394 = t70 + t393;
    t395 = x2 * t394 * y2;
    t396 = t5 * t231;

    t397 = t396 + t395;
    t398 = py1 * t397;
    t399 = py2 * t2;
    t400 = - py3 * t2;

    t401 = t400 + t399;
    t402 = x2 * t401 * y2;
    t403 = t136 + t135;
    t404 = t5 * t403;

    t405 = t404 + t402;
    t406 = px1 * t405;
    t407 = t1 * (t406 + t398 + t392 + t391);

    t408 = t65 + t64;
    t409 = t5 * t408;
    t410 = x2 * t394;
    t411 = t410 + t409;

    t412 = py1 * t411;
    t413 = t5 * t116;
    t414 = x2 * t401;
    t415 = t414 + t413;

    t416 = px1 * t415;
    t417 = py2 * t5;
    t418 = x2 * (t134 + t114);
    t419 = py3 * t2;

    t420 = t419 + t418 + t417;
    t421 = px1 * t420;
    t422 = t265 * y2;
    t423 = x2 * t154;

    t424 = px2 * x2;
    t425 = (t68 + t424) * y2;
    t426 = - py2 * x2;
    t427 = (t133 + t426) * y2;

    t428 = py3 * x2 * y3;
    t429 = t20 + t19;
    t430 = x2 * t429;
    t431 = - px2 * py3 * t2;

    t432 = (t431 + t43 + t430) * y2;
    t433 = t5 * t370;
    t434 = x2 * t145;

    t435 = - px2 * x2 * x3;
    t436 = px2 * t2;
    t437 = (t436 + t435) * y2;
    t438 = px3 * t5 * y3;

    t439 = - px3 * x2 * x3 * y3;
    t440 = py2 * x2 * x3;
    t441 = - py2 * t2;

    t442 = (t441 + t440) * y2;
    t443 = - py3 * t5 * y3;
    t444 = py3 * x2 * x3 * y3;

    t445 = t5 * t287;
    t446 = t78 + t436;
    t447 = x2 * t446;
    t448 = - t2;

    t449 = t448 + 2 * x2 * x3 - t5;
    t450 = px1 * t449;
    t451 = (t98 + t85) * y2;
    t452 = - x2 * y3;

    t453 = x3 * y3;
    t454 = t453 + t452 + (x2 - x3) * y2;
    t455 = px1 * t454;
    t456 = t65 + t314;

    t457 = x2 * t456;
    t458 = (t78 + t457) * y2;
    t459 = x2 * (t293 + t203);

    t460 = - x2 * x3 * y3 + t5 * y3 + (t2 - x2 * x3) * y2;
    t461 = px1 * t460;
    t462 = t5 * t253;

    t463 = t419 + t441;
    t464 = x2 * t463;
    t465 = - py2 * t5;
    t466 = x2 * (t115 + t133);

    t467 = t2 - 2 * x2 * x3 + t5;
    t468 = py1 * t467;
    t469 = py2 * x2;
    t470 = (t134 + t469) * y2;

    t471 = - py2 * x2 * y3;
    t472 = x2 * y3;
    t473 = - x3 * y3;
    t474 = t473 + t472 + (x3 - x2) * y2;

    t475 = py1 * t474;
    t476 = - 2 * py2 * x3;
    t477 = t115 + t476;
    t478 = x2 * t477;

    t479 = (t419 + t478) * y2;
    t480 = py2 * t5 * y3;
    t481 = - 2 * py3 * x3 * y3;

    t482 = x2 * (t481 + t117);
    t483 = x2 * x3 * y3 - t5 * y3 + (t448 + x2 * x3) * y2;

    t484 = py1 * t483;
    t485 = t431 + t43;
    t486 = t485 * t142;
    t487 = t5 * t158;

    t488 = t446 * t142;
    t489 = t5 * t168;
    t490 = t489 + t488;
    t491 = py1 * t490;

    t492 = t463 * t142;
    t493 = t5 * t173;
    t494 = t493 + t492;
    t495 = px1 * t494;

    t496 = x1 * y1 * (t495 + t491 + t487 + t486);
    t497 = t142 * t119;
    t498 = x2 * y2 * t259;

    t499 = t498 + t497;
    t500 = px1 * t499;
    t501 = t29 * (t500 + t381 + t151 + t146);

    t502 = t429 * t142;
    t503 = x2 * t370;
    t504 = y2 * (t503 + t125 + t54);
    t505 = x2 * t158;

    t506 = - px3 * x3 * t142;
    t507 = - px2 * x2 * t147;
    t508 = py3 * x3 * t142;

    t509 = y2 * (t118 + t471);
    t510 = py2 * x2 * t147;
    t511 = - py2 * t142;

    t512 = y2 * (t138 + t107);
    t513 = t172 + t512 + t511;
    t514 = px1 * t513;

    t515 = y2 * t259 + t142 * t255;
    t516 = px1 * t515;
    t517 = py1 * t454;

    t518 = - py2 * x3 * t142;
    t519 = t108 + t384;
    t520 = x2 * t519;

    t521 = y2 * (t520 + t307 + t135);
    t522 = - py3 * x2 * t147;
    t523 = py2 * t142;

    t524 = y2 * (t108 + t137);
    t525 = - t147 + 2 * y2 * y3 - t142;
    t526 = py1 * t525;

    t527 = x2 * t147 + y2 * (t473 + t452) + x3 * t142;
    t528 = py1 * t527;
    t529 = px1 * t474;

    t530 = px2 * x3 * t142;
    t531 = px3 * x2 * t147;

    t532 = - x2 * t147 + y2 * (t453 + t472) - x3 * t142;
    t533 = px1 * t532;

    t534 = - px2 * t142;
    t535 = t147 - 2 * y2 * y3 + t142;
    t536 = px1 * t535;

    t537 = t447 + t445;
    t538 = py1 * t537;
    t539 = t464 + t462;
    t540 = px1 * t539;

    t541 = 2 * px3 * py2 * t2;
    t542 = - 2 * px2 * py3 * t2;
    t543 = x2 * t446 * y2;

    t544 = t5 * t205;
    t545 = t544 + t543;
    t546 = py1 * t545;
    t547 = x2 * t463 * y2;

    t548 = t5 * t119;
    t549 = t548 + t547;
    t550 = px1 * t549;
    t551 = x2 * t265;

    t552 = (t389 + t10 + t551) * y2;
    t553 = t5 * t154;
    t554 = 2 * px3 * t2;

    t555 = (t554 + t393 + t110) * y2;
    t556 = t5 * t90;
    t557 = py3 * x2 * x3;

    t558 = - 2 * py3 * t2;
    t559 = (t558 + t399 + t557) * y2;
    t560 = py2 * x2 * x3 * y3;

    t561 = t138 + t361;
    t562 = t5 * t561;
    t563 = t390 * t142;
    t564 = t5 * t150;

    t565 = - px2 * t2 * t142;
    t566 = - px3 * t5 * t147;
    t567 = t566 + t214 + t565;

    t568 = py1 * t567;
    t569 = py2 * t2 * t142;
    t570 = x2 * y2 * (t118 + t135);

    t571 = py3 * t5 * t147;
    t572 = t571 + t570 + t569;
    t573 = px1 * t572;
    t574 = t86 + t68;

    t575 = x2 * t574;
    t576 = (t78 + t575) * y2;
    t577 = 2 * px2 * x3 * y3;

    t578 = x2 * (t87 + t577);
    t579 = px1 * t527;

    t580 = - t5 * t147 + 2 * x2 * x3 * y2 * y3 - t2 * t142;
    t581 = px1 * t580;
    t582 = t305 + t133;

    t583 = x2 * t582;
    t584 = (t419 + t583) * y2;
    t585 = x2 * (t136 + t306);

    t586 = py1 * t532;
    t587 = - py3 * t2 * t142;
    t588 = x2 * y2 * (t136 + t117);

    t589 = - py2 * t5 * t147;
    t590 = t5 * t147 - 2 * x2 * x3 * y2 * y3 + t2 * t142;

    t591 = py1 * t590;
    t592 = t400 + t466 + t465;
    t593 = px1 * t592;
    t594 = t309 + t279;

    t595 = t198 + t311;
    t596 = x2 * t378;
    t597 = t596 + t408 * y2;
    t598 = py1 * t597;

    t599 = t256 + t116 * y2;
    t600 = px1 * t599;
    t601 = t178 + t366 + t534;

    t602 = py1 * t601;
    t603 = t181 + t524 + t523;
    t604 = px1 * t603;
    t605 = t265 * t142;

    t606 = t423 + t144 + t143;
    t607 = y2 * t606;
    t608 = x2 * t150;
    t609 = 2 * py2 * x3 * y3;

    t610 = t362 + t137;
    t611 = x2 * t610;
    t612 = y2 * (t611 + t118 + t609);

    t613 = py1 * t449;
    t614 = t419 + t613 + t418 + t417;
    t615 = py1 * t460;

    t616 = py1 * t535;
    t617 = t616 + t172 + t512 + t511;
    t618 = t134 + t304;

    t619 = t618 * t142;
    t620 = - py3 * x2 * y3;
    t621 = y2 * (t135 + t620);

    t622 = x2 * (t388 + t180);
    t623 = px1 * t467;
    t624 = t623 + t78 + t102 + t101;

    t625 = px1 * t483;
    t626 = px1 * t525;
    t627 = t167 + t626 + t354 + t353;

    t628 = - 2 * px2 * x3;
    t629 = t98 + t628;
    t630 = t629 * t142;
    t631 = - 2 * px3 * t147;

    t632 = x2 * (t631 + t177);
    t633 = - 2 * px2 * py3 * x3 * y3;
    t634 = t633 + t197;

    t635 = - 2 * px3 * py2 * t147;
    t636 = t142 * t403;
    t637 = x2 * y2 * t173;

    t638 = t637 + t636;
    t639 = px1 * t638;
    t640 = t589 + t588 + t587;
    t641 = px1 * t640;

    t642 = px1 * t590;
    t643 = py1 * t580;

    t644 = (x0 * (px0 * (y1 * (x1 * (t528 + t522 + t612 + t518)
			       + t643 + t571 + t570 + t569)
			 + t29 * t515 + x1 * t638 + t1 * (t615 + t444 + t443 + t442))
		  + py0 * (y1 * (x1 * (t533 + t531 + t331 + t530)
				 + t642 + t566 + t214 + t565)
			   + x1 * t234 + t29 * t379 + t1 * (t625 + t439 + t438 + t437))
		  + y1 * (x1 * (px1 * (t622 + t621 + t619) + py1 * (t632 + t299 + t630)
				+ t608 + t607 + t605)
			  + t641 + t243 + t564 + t563)
		  + x1 * (t639 + t235 + x2 * y2 * (t284 + t635) + t142 * t634)
		  + t29 * (t175 + t170)
		  + t1 * (px1 * (t482 + t480 + t479) + py1 * (t459 + t79 + t458) + t434
			  + t433 + t432))
	    + y0 * (x0 * (py0 * (x1 * (t579 + t632 + t299 + t630)
				 + t489 + t29 * t627
				 + y1 * (x1 * t597 + t625 + t556 + t112 + t555) + t488
				 + t624 * t1)
			  + px0 * (x1 * (t586 + t622 + t621 + t619)
				   + t29 * t617 + t493
				   + y1 * (x1 * t599 + t615 + t562 + t560 + t559) + t492
				   + t614 * t1)
			  + x1 * (px1 * (t522 + t612 + t518) + py1 * (t531 + t331 + t530)
				  + t608 + t607 + t605)
			  + t29 * (t604 + t602) + t487
			  + y1 * (x1 * (t600 + t598 + x2 * t595 + t594 * y2)
				  + px1 * (t585 + t480 + t584) + py1 * (t578 + t79 + t576) + t267
				  + t553 + t552) + t486 + (t593 + t302) * t1)
		    + px0 * (x1 * (t591 + t589 + t588 + t587)
			     + t29 * (t586 + t510 + t509 + t508)
			     + y1 * (x1 * (t484 + t585 + t480 + t584) + t548 + t547) + t415 * t1)
		    + py0 * (x1 * (t581 + t241 + t240 + t239)
			     + t29 * (t579 + t507 + t250 + t506)
			     + y1 * (x1 * (t461 + t578 + t79 + t576) + t544 + t543) + t411 * t1)
		    + x1 * (t573 + t568 + t564 + t563)
		    + t29 * (px1 * (t522 + t521 + t518) + py1 * (t531 + t225 + t530) + t505
			     + t504 + t502)
		    + y1 * (x1 * (px1 * (t562 + t560 + t559) + py1 * (t556 + t112 + t555)
				  + t267 + t553 + t552)
			    + t550 + t546 + t5 * (t322 + t273) + x2 * (t542 + t541) * y2)
		    + (t540 + t538) * t1)
	    + t161 * (py0 * (y1 * (x1 * (t536 + t178 + t366 + t534)
				   + t533 + t531 + t225 + t530)
			     + x1 * t169 + t208 + t1 * (t529 + t204 + t292 + t425) + t206)
		      + px0 * (y1 * (t528 + x1 * (t181 + t526 + t524 + t523) + t522 + t521
				     + t518)
			       + x1 * t174 + t498 + t1 * (t517 + t118 + t428 + t427) + t497)
		      + x1 * (t516 + t383)
		      + y1 * (x1 * (t514 + t382) + px1 * (t510 + t509 + t508)
			      + py1 * (t507 + t250 + t506) + t505 + t504
			      + t502) + t151
		      + t1 * (px1 * (t136 + t471 + t470) + py1 * (t87 + t249 + t451) + t423
			      + t422) + t146) + t501 + t496
	    + t14 * (px0 * (x1 * (t484 + t482 + t480 + t479)
			    + t29 * (t475 + t136 + t471 + t470) + t404 + t402
			    + (x1 * (t468 + t400 + t466 + t465) + t464 + t462) * y1)
		     + py0 * (x1 * (t461 + t459 + t79 + t458)
			      + t29 * (t455 + t87 + t249 + t451) + t396 + t395
			      + (x1 * (t70 + t450 + t69 + t67) + t447 + t445) * y1)
		     + x1 * (px1 * (t444 + t443 + t442) + py1 * (t439 + t438 + t437) + t434
			     + t433 + t432)
		     + t29 * (px1 * (t118 + t428 + t427) + py1 * (t204 + t292 + t425) + t423
			      + t422) + t392 + t391
		     + (x1 * (t421 + t104) + t416 + t412) * y1) + t407);
    t645 = t5 * t265;

    t646 = t115 + t114 + t132;
    t647 = px1 * t646;
    t648 = x2 * t485;
    t649 = t32 * t5;

    t650 = t70 + t393 + t73 * t5;
    t651 = t400 + t399 + t106 * t5;

    t652 = t540 + x1 * (px1 * t651 + py1 * t650 + t389 + t10 + t649) + t538 + t648
	+ t29 * (t647 + t357 + t20 + t19 + t18) + t645;
    t653 = t648 + t645;

    t654 = t392 + t391;
    t655 = px1 * t654;
    t656 = t309 + t19;
    t657 = x2 * t656;

    t658 = (t389 + t657) * y2;
    t659 = px3 * py2 * t5 * y3;
    t660 = x2 * (t144 + t273);

    t661 = - px3 * py2 * t5;
    t662 = t431 + t27 + t661;
    t663 = px1 * t662 + t24;

    t664 = t5 * t429;
    t665 = x2 * t390;
    t666 = t665 + t664;
    t667 = px3 * py2 * x2;

    t668 = (t20 + t667) * y2;
    t669 = x2 * t485 * y2;
    t670 = t5 * t145;
    t671 = t670 + t669;

    t672 = px1 * t671;
    t673 = t26 + t52;
    t674 = x2 * t673;
    t675 = (t389 + t674) * y2;

    t676 = x2 * (t633 + t54);
    t677 = px3 * t5;
    t678 = t436 + t69 + t677;

    t679 = px1 * t678 + t37 + t60 + t59;
    t680 = - px3 * x2;

    t681 = t203 + t298 + (t64 + t680) * y2;
    t682 = px1 * t545;
    t683 = - px3 * t5 * y3;

    t684 = t578 + t683 + (t393 + t575) * y2;
    t685 = 2 * py3 * x3;
    t686 = t685 + t476;

    t687 = 2 * py2 * t2;
    t688 = px1 * (t419 + t441 + t131 * t5);
    t689 = - px2 * py3 * x2;

    t690 = 2 * px2 * py3 * x3;
    t691 = (t690 + t94 + t689) * y2;

    t692 = t330 + t204 + (t98 + t628 + t424) * y2;
    t693 = t134 + t133 + t319;

    t694 = px1 * (t140 + t118 + t117 + t693 * y2);
    t695 = (t542 + t43 + t9) * y2;

    t696 = t5 * t312;
    t697 = 2 * px2 * t2;
    t698 = t5 * t316 + t112 + (t78 + t697 + t110) * y2;

    t699 = x2 * t253;
    t700 = t5 * t255;
    t701 = x2 * t403;

    t702 = px1 * (t701 + t700 + (t419 + t441 + t699) * y2);
    t703 = px2 * py3 * x2 * x3;

    t704 = (t10 + t703) * y2;
    t705 = px3 * py2 * x2 * x3 * y3;
    t706 = (t20 + t279 + t689) * y2;

    t707 = t439 + t111 + (t70 + t435) * y2;
    t708 = t224 + t204 + (t296 + t64 + t424) * y2;

    t709 = - 2 * py2;
    t710 = 2 * py3;
    t711 = py1 * t678;

    t712 = t459 + t683 + (t393 + t457) * y2;
    t713 = x2 * t116;
    t714 = t5 * t139;

    t715 = px1 * (t120 + t714 + (t400 + t399 + t713) * y2);
    t716 = 2 * px2 * py3;

    t717 = (t94 + (t716 + t15) * x2) * y2;
    t718 = - 2 * px2;

    t719 = t221 + t128 + t249 + (t98 + (px3 + t718) * x2) * y2;

    t720 = px1 * (t256 + t136 + t135 + t646 * y2);
    t721 = - px2 * py3 * t2 * t142;

    t722 = - px3 * py2 * t5 * t147;
    t723 = t722 + t237 + t721;
    t724 = - px2 * py3 * x3 * t142;

    t725 = y2 * (t54 + t124);
    t726 = px1 * y2 * t257;
    t727 = - px3 * py2 * x2 * t147;

    t728 = y2 * (t87 + t127);
    t729 = t531 + t728 + t530;
    t730 = px2 * py3 * t2 * t142;

    t731 = px3 * py2 * t5 * t147;
    t732 = px1 * t397;
    t733 = t251 + t299 + t248;

    t734 = px2 * t2 * t142;
    t735 = px3 * t5 * t147;
    t736 = t735 + t240 + t734;

    t737 = t389 + t10 + t649;
    t738 = t731 + t189 + t730;
    t739 = px1 * t738;

    t740 = x2 * t165;
    t741 = t740 + t204 + t203;
    t742 = py1 * y2 * t741;
    t743 = py1 * t736;

    t744 = px2 * py3 * t142;
    t745 = px1 * t567;
    t746 = t148 + t364 + t744;

    t747 = px3 * py2 * t5;
    t748 = t389 + t95 + t747;
    t749 = (t26 + t122) * y2;

    t750 = x2 * t280;
    t751 = (t431 + t750) * y2;
    t752 = - px3 * py2 * t5 * y3;

    t753 = x2 * (t322 + t143);
    t754 = - px3 * t5;
    t755 = t393 + t102 + t754;

    t756 = t128 + t292 + (t68 + t126) * y2;
    t757 = x2 * t297;
    t758 = x2 * (t204 + t327);

    t759 = t758 + t438 + (t436 + t757) * y2;
    t760 = (t94 + t667) * y2;

    t761 = t203 + t249 + (t98 + t680) * y2;
    t762 = px1 * (t140 + t253 * y2);

    t763 = - px3 * py2 * x2 * x3;
    t764 = (t43 + t763) * y2;
    t765 = - px2 * py3 * x2 * x3 * y3;

    t766 = px3 * x2 * x3;
    t767 = px2 * x2 * x3 * y3;
    t768 = t767 + t79 + (t78 + t766) * y2;

    t769 = px1 * (t120 + t700 + (t419 + t441 + t713) * y2);
    t770 = t501 + t496 + t407;

    t771 = px3 * py2 * x3 * t142;
    t772 = y2 * (t313 + t633 + t54);

    t773 = px2 * py3 * x2 * t147;
    t774 = - px3 * py2 * t142;
    t775 = t149 + t346 + t774;

    t776 = y2 * (t317 + t87 + t577);
    t777 = t507 + t776 + t506;
    t778 = px3 * t142;

    t779 = t177 + t354 + t778;
    t780 = y2 * (t144 + t272);
    t781 = y2 * (t203 + t292);

    t782 = t531 + t781 + t530;
    t783 = px1 * (t336 + t258 + t333);
    t784 = t690 + t94;

    t785 = x2 * t784;
    t786 = (t431 + t785) * y2;
    t787 = x2 * (t125 + t197);

    t788 = x2 * t629;
    t789 = x2 * (t221 + t128);
    t790 = t789 + t438 + (t436 + t788) * y2;

    t791 = - 2 * py2 * t2;
    t792 = 2 * py3 * t2;
    t793 = 2 * px2 * py3 * t2;

    t794 = (t793 + t10 + t42) * y2;
    t795 = t5 * t324;
    t796 = - 2 * px2 * t2;

    t797 = t5 * t329 + t80 + (t70 + t796 + t77) * y2;

    t798 = px1 * (t701 + t714 + (t400 + t399 + t699) * y2);

    t799 = px1 * (t5 * t259 + t401 * t142);
    t800 = t429 * y2;
    t801 = t503 + t800;

    t802 = t487 + t486;
    t803 = t673 * t142;
    t804 = - 2 * px2 * py3 * t147;

    t805 = x2 * (t804 + t148);
    t806 = 2 * px2 * t147;

    t807 = x2 * (t178 + t806) + t728 + t574 * t142;
    t808 = py1 * t755;
    t809 = py1 * t779;

    t810 = y2 * (t58 + t144 + t273);
    t811 = y2 * (t91 + t293 + t203);

    t812 = t507 + t811 + t506;
    t813 = px1 * (t260 + t335 + t254);
    t814 = 2 * py2 * t147;

    t815 = - 2 * py3 * t147;
    t816 = (t389 + t42) * y2;
    t817 = - py2 * py3 * t2;

    t818 = (t817 + py2 * py3 * x2 * x3) * y2;
    t819 = - py2 * py3 * t5 * y3;

    t820 = py2 * py3 * x2 * x3 * y3;
    t821 = px1 * (t820 + t819 + t818);
    t822 = - py2 * py3 * t5;

    t823 = 2 * py2 * py3 * x2 * x3;
    t824 = px1 * (t817 + t823 + t822);
    t825 = (t431 + t9) * y2;

    t826 = py2 * py3 * t2;
    t827 = (t826 - py2 * py3 * x2 * x3) * y2;
    t828 = py2 * py3 * t5 * y3;

    t829 = - py2 * py3 * x2 * x3 * y3;
    t830 = px1 * (t829 + t828 + t827);

    t831 = (py2 * py3 * x2 - py2 * py3 * x3) * y2;
    t832 = - py2 * py3 * x2 * y3;

    t833 = py2 * py3 * x3 * y3;
    t834 = px1 * (t833 + t832 + t831);

    t835 = (t690 + t94 + t122) * y2;
    t836 = px1 * t693;
    t837 = - py2 * t5 * y3;

    t838 = t560 + t837 + (t400 + t557) * y2;
    t839 = x2 * t205;

    t840 = py1 * (t839 + x2 * t408 * y2);
    t841 = (t20 + t51) * y2;
    t842 = - py3 * x2;

    t843 = py2 * x2 * y3;
    t844 = t135 + t843 + (t115 + t842) * y2;

    t845 = py1 * (t740 + t87 + t128 + (t98 + t68 + t63) * y2);
    t846 = py2 * py3 * t5;

    t847 = - 2 * py2 * py3 * x2 * x3;
    t848 = - py2 * x2 * x3;
    t849 = - py3 * x2 * x3 * y3;

    t850 = t849 + t480 + (t419 + t848) * y2;
    t851 = (py2 * py3 * x3 - py2 * py3 * x2) * y2;

    t852 = py2 * py3 * x2 * y3;
    t853 = - py2 * py3 * x3 * y3;
    t854 = x2 * t561;

    t855 = t854 + t136 + (t305 + t133 + t469) * y2;
    t856 = py2 * py3 * t2 * t142;

    t857 = - 2 * py2 * py3 * x2 * x3 * y2 * y3;
    t858 = py2 * py3 * t5 * t147;

    t859 = px1 * (t858 + t857 + t856);
    t860 = - py2 * py3 * x3 * t142;

    t861 = y2 * (t833 + t852);
    t862 = - py2 * py3 * x2 * t147;

    t863 = px1 * (t862 + t861 + t860);
    t864 = - py2 * py3 * t2 * t142;

    t865 = 2 * py2 * py3 * x2 * x3 * y2 * y3;
    t866 = - py2 * py3 * t5 * t147;

    t867 = py3 * t2 * t142;
    t868 = py2 * t5 * t147;
    t869 = t868 + t570 + t867;

    t870 = py2 * py3 * x3 * t142;
    t871 = y2 * (t853 + t832);
    t872 = py2 * py3 * x2 * t147;

    t873 = - py3 * x3 * t142;
    t874 = - py2 * x2 * t147;
    t875 = t874 + t521 + t873;

    t876 = py2 * x3 * t142;
    t877 = py3 * x2 * t147;
    t878 = t877 + t509 + t876;

    t879 = t287 * t142;
    t880 = t596 + t87 + t128;
    t881 = y2 * t880;
    t882 = x2 * t207;

    t883 = py1 * (t882 + t881 + t879);
    t884 = py1 * t662;

    t885 = px1 * (t826 + t847 + t846);
    t886 = 2 * px3 * py2;

    t887 = (t94 + (t31 + t886) * x2) * y2;
    t888 = px1 * (t853 + t852 + t851);

    t889 = py1 * t738;
    t890 = px1 * (t866 + t865 + t864);

    t891 = px1 * (t872 + t871 + t870);
    t892 = t656 * t142;
    t893 = x2 * (t157 + t635);

    t894 = t221 + t577;
    t895 = x2 * t253 * y2;
    t896 = t701 + t895;
    t897 = px1 * t896;

    t898 = (t20 + t279 + t122) * y2;

    t899 = py1 * (t596 + t204 + t203 + (t65 + t64 + t97) * y2);
    t900 = t385 + t107;

    t901 = x2 * t900;
    t902 = t901 + t136 + (t115 + t476 + t469) * y2;
    t903 = px1 * t869;

    t904 = t874 + t612 + t873;
    t905 = t408 * t142;
    t906 = y2 * t741;
    t907 = x2 * t168;

    t908 = py1 * (t907 + t906 + t905);
    t909 = - py2 * py3 * t142;

    t910 = 2 * py2 * py3 * y2 * y3;
    t911 = - py2 * py3 * t147;

    t912 = px1 * (t911 + t910 + t909);
    t913 = t912 + py1 * t376;

    t914 = t481 + t117 + t428 + (t133 + (py3 + t709) * x2) * y2;
    t915 = 2 * px3;

    t916 = t138 + t137 + t131 * y2;
    t917 = px1 * t916;

    t918 = py1 * (t167 + t166 + t73 * t142);
    t919 = py3 * t142;
    t920 = t171 + t524 + t919;

    t921 = px1 * t920;
    t922 = py2 * py3 * t142;
    t923 = - 2 * py2 * py3 * y2 * y3;

    t924 = py2 * py3 * t147;
    t925 = py1 * t513 + t924 + t923 + t922;
    t926 = py1 * t420;

    t927 = py1 * t640;
    t928 = t685 + t114;
    t929 = x2 * (t172 + t814) + t621 + t928 * t142;

    t930 = px1 * (t924 + t923 + t922);
    t931 = t930 + py1 * t347;

    t932 = py1 * t920 + t911 + t910 + t909;
    t933 = t315 + t222;
    t934 = py1 * t654;

    t935 = (t10 + t750) * y2;
    t936 = t824 + py1 * t263;
    t937 = py1 * t671;

    t938 = (t19 + t689) * y2;
    t939 = (t10 + t785) * y2;
    t940 = t296 + t314;

    t941 = py1 * (t78 + t436 + t62 * t5);
    t942 = (t26 + t52 + t667) * y2;

    t943 = py1 * (t740 + t204 + t203 + t99 * y2);

    t944 = t611 + t118 + (t134 + t304 + t426) * y2;
    t945 = (t431 + t541 + t42) * y2;

    t946 = t5 * t199;
    t947 = t5 * t900 + t560 + (t419 + t791 + t557) * y2;
    t948 = x2 * t287;

    t949 = t5 * t378;
    t950 = py1 * (t289 + t949 + (t78 + t436 + t948) * y2);

    t951 = - py3 * t5;
    t952 = t441 + t466 + t951;
    t953 = py1 * t952 + t826 + t847 + t846;

    t954 = py3 * x2;
    t955 = t117 + t620 + (t114 + t954) * y2;
    t956 = py1 * t549;

    t957 = py3 * t5 * y3;
    t958 = t585 + t957 + (t399 + t583) * y2;
    t959 = (t389 + t763) * y2;

    t960 = (t309 + t19 + t667) * y2;
    t961 = - 2 * px3;
    t962 = px1 * t952;
    t963 = x2 * t408;

    t964 = t5 * t165;
    t965 = py1 * (t839 + t964 + (t70 + t393 + t963) * y2);

    t966 = t482 + t957 + (t399 + t478) * y2;
    t967 = - 2 * px3 * py2;

    t968 = (t26 + (t16 + t967) * x2) * y2;

    t969 = t307 + t135 + t471 + (t134 + (t130 + t359) * x2) * y2;

    t970 = py1 * (t596 + t87 + t128 + t66 * y2);
    t971 = t444 + t837 + (t400 + t440) * y2;

    t972 = t520 + t118 + (t685 + t114 + t426) * y2;
    t973 = py1 * t405;

    t974 = t877 + t621 + t876;
    t975 = - py2 * t2 * t142;
    t976 = - py3 * t5 * t147;

    t977 = t976 + t588 + t975;
    t978 = py1 * y2 * t880;
    t979 = y2 * (t136 + t843);

    t980 = t522 + t979 + t518;
    t981 = py1 * t276;
    t982 = py1 * t572;
    t983 = px1 * y2 * t334;

    t984 = px1 * t977;
    t985 = (t94 + t51) * y2;
    t986 = (t43 + t657) * y2;

    t987 = (t26 + t689) * y2;
    t988 = t117 + t471 + (t134 + t954) * y2;

    t989 = py1 * (t740 + t287 * y2);
    t990 = (t431 + t703) * y2;
    t991 = - py3 * x2 * x3;

    t992 = - py2 * x2 * x3 * y3;
    t993 = t992 + t480 + (t419 + t991) * y2;

    t994 = py1 * (t839 + t949 + (t78 + t436 + t963) * y2);
    t995 = py3 * t5;

    t996 = t399 + t418 + t995;
    t997 = t135 + t428 + (t133 + t842) * y2;
    t998 = x2 * t928;

    t999 = x2 * (t118 + t609);
    t1000 = t999 + t443 + (t441 + t998) * y2;

    t1001 = y2 * (t901 + t136 + t306);
    t1002 = t510 + t1001 + t508;
    t1003 = - py3 * t142;

    t1004 = t180 + t512 + t1003;
    t1005 = y2 * (t117 + t428);
    t1006 = t522 + t1005 + t518;

    t1007 = py1 * (t907 + t881 + t905);
    t1008 = y2 * (t854 + t481 + t117);

    t1009 = t510 + t1008 + t508;
    t1010 = 2 * px3 * t147;

    t1011 = py1 * (t5 * t207 + t394 * t142);
    t1012 = t784 * t142;

    t1013 = 2 * px3 * py2 * t147;
    t1014 = x2 * (t149 + t1013);

    t1015 = py1 * (t882 + t906 + t879);
    t1016 = x2 * (t181 + t387) + t979 + t582 * t142;

    t1017 = (t43 + t674) * y2;
    t1018 = x2 * t618;
    t1019 = x2 * (t307 + t135);

    t1020 = t1019 + t443 + (t441 + t1018) * y2;
    t1021 = - 2 * px3 * t2;

    t1022 = - 2 * px3 * py2 * t2;
    t1023 = (t389 + t1022 + t9) * y2;
    t1024 = t5 * t57;

    t1025 = t5 * t610 + t849 + (t400 + t687 + t848) * y2;

    t1026 = py1 * (t289 + t964 + (t70 + t393 + t948) * y2);
    t1027 = px1 * t996;

    t1028 = px1 * t1004;
    t1029 = x2 * t429 * y2;
    t1030 = (t436 + t110) * y2;

    t1031 = (t441 + t557) * y2;
    t1032 = (t393 + t77) * y2;
    t1033 = (t399 + t848) * y2;

    t1034 = (t26 + t94 + t18) * y2;
    t1035 = (t64 + t85) * y2;
    t1036 = (t114 + t469) * y2;

    t1037 = (t98 + t628 + t126) * y2;
    t1038 = (t134 + t304 + t842) * y2;

    t1039 = (t20 + t19 + t96) * y2;
    t1040 = (t296 + t64 + t126) * y2;

    t1041 = (t685 + t114 + t842) * y2;
    t1042 = (t98 + (t961 + px2) * x2) * y2;

    t1043 = t456 * t142;
    t1044 = x2 * (t1010 + t166);

    t1045 = (t134 + (t710 + t105) * x2) * y2;
    t1046 = t477 * t142;

    t1047 = x2 * (t815 + t171);
    t1048 = t32 * t142;
    t1049 = t171 + t526 + t524 + t919;

    t1050 = t536 + t166 + t366 + t365;
    t1051 = (t389 + t10 + t430) * y2;

    t1052 = (t393 + t766) * y2;
    t1053 = (t399 + t991) * y2;
    t1054 = t17 * t5;

    t1055 = (t431 + t43 + t551) * y2;
    t1056 = (t1021 + t436 + t77) * y2;
    t1057 = t5 * t223;

    t1058 = (t792 + t441 + t848) * y2;
    t1059 = t5 * t519;
    t1060 = t338 * y2;

    t1061 = (t86 + t68 + t680) * y2;
    t1062 = (t305 + t133 + t954) * y2;

    t1063 = (t115 + t426) * y2;
    t1064 = (t400 + t1018) * y2;
    t1065 = (t65 + t424) * y2;

    t1066 = (t70 + t788) * y2;
    t1067 = (t70 + t757) * y2;
    t1068 = (t400 + t998) * y2;

    t1069 = t21 * y2;
    t1070 = (t68 + (t915 + t61) * x2) * y2;

    t1071 = (t133 + (t360 + py2) * x2) * y2;
    t1072 = (t115 + t476 + t954) * y2;

    t1073 = (t65 + t314 + t680) * y2;

    trans->m[0][0]
	= (x0 * (px0 * (x1 * (px1 * (y2 * (t388 + t387) + t142 * t386)
			      + t383 + t372 + t371)
			+ y1 * (x1 * (t369 + t382 + t156 + t346 + t345)
				+ t337 + py1 * t301 + t285 + t283 + t281) + t381 + t151
			+ t1 * (t141 + py1 * t92 + t58 + t54 + t53) + t146)
		 + py0 * (y1 * (x1 * t380 + px1 * t332 + t219 + t218 + t217)
			  + px1 * t234 + px1 * x1 * t379 + t1 * (px1 * t129 + t49 + t48 + t47))
		 + y1 * (x1 * t377 + px1 * (t202 + t326 + t196) + t195) + px1 * t374
		 + px1 * x1 * t373 + t1 * (px1 * (t125 + t124 + t123) + t269))
	   + y0 * (x0 * (px0 * (t261 + x1 * (t369 + t368 + t157 + t364 + t363) + py1 * t227
				+ t202
				+ y1
				* (x1
				   * (px1 * (t362 + t361 + (t360 + t359) * y2)
				      + t358 + t153 + t55 + t339)
				   + t320 + py1 * t294 + t144 + t273 + t272 + t271)
				+ t201 + t196 + (t357 + t20 + t19 + t18) * t1)
			 + py0 * (x1 * t356 + px1 * t252 + t194
				  + y1 * (px1 * t318 + px1 * x1 * t349 + t84 + t83 + t82)
				  + t193 + t192 + px1 * t99 * t1) + x1 * t348
			 + px1 * (t247 + t246 + t244) + t278
			 + y1 * (px1 * (t313 + t54 + t310) + t50 + px1 * x1 * t340)
			 + px1 * t338 * t1)
		   + px0 * (x1 * (t337 + py1 * t332 + t202 + t326 + t196)
			    + t321 + px1 * t29 * t182 + t190
			    + y1 * (x1 * (t320 + py1 * t318 + t313 + t54 + t310)
				    + px1 * (x2 * t308 + x2 * (t305 + t304) * y2) + t303 + t267
				    + t266) + t189 + t188 + (t302 + t10 + t27 + t25) * t1)
		   + py0 * (x1 * (px1 * t301 + t194 + t193 + t192)
			    + t295 + px1 * t29 * t179 + t186
			    + y1 * (x1 * (px1 * t294 + t49 + t48 + t47) + px1 * t290) + t185 + t184
			    + (t286 + t3 + t23 + t22) * t1)
		   + x1 * (px1 * (t285 + t283 + t281) + t278) + t277 + t275 + px1 * t29 * t274
		   + y1 * (x1 * (px1 * (t144 + t273 + t272 + t271) + t269) + px1 * t268)
		   + (t264 + t262) * t1)
	   + px0 * (y1 * (x1 * (t261 + py1 * t252 + t247 + t246 + t244)
			  + t243 + t238 + t237 + t236)
		    + x1 * (t235 + t230 + t229) + px1 * t29 * t174
		    + t1 * (t121 + py1 * t81 + t46 + t45 + t44))
	   + py0 * (y1 * (x1 * (px1 * t227 + t219 + t218 + t217)
			  + px1 * t216 + t212 + t211 + t210)
		    + px1 * t29 * t169 + px1 * x1 * t209 + t1 * (px1 * t113 + t40 + t39 + t38))
	   + y1 * (x1 * (px1 * (t202 + t201 + t196) + t195) + px1 * t191 + t187)
	   + px0 * t161 * t183 + px1 * t29 * t160 + px1 * x1 * t152
	   + t14 * (px0 * (x1 * (t141 + py1 * t129 + t125 + t124 + t123)
			   + t121 + py1 * t113 + px1 * t29 * t109 + t13 + t12 + t11
			   + (t104 + t43 + x1 * (t100 + t26 + t94 + t96) + t95 + t93) * y1)
		    + py0 * (x1 * (px1 * t92 + t84 + t83 + t82)
			     + px1 * t81 + px1 * t29 * t76 + t7 + t6 + t4
			     + (px1 * t71 + t37 + px1 * x1 * t66 + t60 + t59) * y1)
		    + x1 * (px1 * (t58 + t54 + t53) + t50) + px1 * (t46 + t45 + t44) + t41
		    + px1 * t29 * t36 + (px1 * t28 + t24 + px1 * x1 * t21) * y1)
	   + t1 * (px1 * (t13 + t12 + t11) + t8));

    trans->m[0][1] =
	(t161 * (px0 * (x1 * (t382 + t156 + t346 + t345)
			+ py1 * t733 + t247
			+ y1 * (t694 + x1 * (t358 + t153 + t55 + t339) + py1 * t681
				+ t144 + t282 + t668) + t726 + t283 + t244
			+ px1 * t646 * t1)
		 + py0 * (x1 * (px1 * t601 + t343 + t342 + t341)
			  + px1 * t729 + t219
			  + y1 * (px1 * t692 + px1 * x1 * t76 + t49 + t48 + t47) + t218
			  + t217 + px1 * t66 * t1) + x1 * (px1 * t746 + t375)
		 + px1 * (t727 + t725 + t724) + t195
		 + y1 * (px1 * (t325 + t143 + t691) + t269 + px1 * x1 * t36)
		 + px1 * t21 * t1)
	 + x0 * (py0 * (t29 * t356 + t745 + t212
			+ y1
			* (x1 * (px1 * t719 + t84 + t83 + t82)
			   + px1 * t698 + t40 + t39 + t38) + px1 * x1 * y2 * t741
			+ t211 + t210 + px1 * t650 * t1)
		 + px0 * (t29 * (t602 + t148 + t364 + t744)
			  + t743 + t722
			  + y1 * (x1 * (t720 + py1 * t708 + t200 + t143 + t706)
				  + t702 + py1 * t684 + t676 + t659 + t675)
			  + x1 * (t607 + px1 * y2 * (x2 * (t362 + t361) + t481 + t609) + t742)
			  + t237 + t721 + px1 * t651 * t1) + t29 * t348 + t739 + t187
		 + y1 * (x1 * (px1 * (t125 + t197 + t245 + t717) + t50)
			 + px1 * (t696 + t13 + t695) + t8) + px1 * x1 * y2 * t606
		 + px1 * t737 * t1)
	 + py0 * (x1 * (px1 * t736 + t186 + t185 + t184)
		  + t29 * (px1 * t733 + t194 + t193 + t192)
		  + y1 * (x1 * (px1 * t712 + t7 + t6 + t4) + t732) + px1 * t537 * t1)
	 + px0 * (x1 * (t568 + t731 + t189 + t730)
		  + t29 * (py1 * t729 + t727 + t726 + t725 + t724)
		  + y1 * (x1 * (t715 + py1 * t707 + t705 + t12 + t704) + t546 + t670 + t669)
		  + px1 * t539 * t1) + x1 * (px1 * t723 + t275)
	 + t29 * (px1 * (t247 + t283 + t244) + t278)
	 + y0 * (x0 * (px0 * (x1 * (t720 + py1 * t719 + t125 + t197 + t245 + t717)
			      + t715 + py1 * t712 + t29 * (t162 + t35 + t34 + t33) + t660
			      + t659 + t658
			      + (t688 + t711 + t431
				 + x1
				 * (px1 * (t305 + t304 + (t710 + t709) * x2)
				    + t100 + t26 + t94 + t96) + t27 + t661)
			      * y1)
		       + py0 * (x1 * (px1 * t708 + t49 + t48 + t47)
				+ px1 * t707 + px1 * t29 * t349 + t40 + t39 + t38
				+ (t286 + t3 + px1 * x1 * t99 + t23 + t22) * y1)
		       + x1 * (px1 * (t200 + t143 + t706) + t269) + px1 * (t705 + t12 + t704)
		       + t8 + px1 * t29 * t340 + (t264 + t262 + px1 * x1 * t338) * y1)
		 + px0 * (x1 * (t702 + py1 * t698 + t696 + t13 + t695)
			  + t29 * (t694 + py1 * t692 + t325 + t143 + t691) + t398 + t392 + t391
			  + (x1 * (t688 + t104 + t43 + t95 + t93)
			     + px1 * (x2 * (t558 + t687) + t5 * t686) + t412 + t665 + t664)
			  * y1)
		 + py0 * (x1 * (px1 * t684 + t7 + t6 + t4) + t682
			  + t29 * (px1 * t681 + t84 + t83 + t82)
			  + (px1 * t411 + x1 * t679) * y1)
		 + x1 * (px1 * (t676 + t659 + t675) + t41) + t672
		 + t29 * (px1 * (t144 + t282 + t668) + t50) + (px1 * t666 + x1 * t663) * y1)
	 + y1 * (x1 * (px1 * (t660 + t659 + t658) + t41) + t655) + px1 * t653 * t1
	 + px0 * t652 * t14)
	;

    trans->m[0][2] =
	(x0 * (px0 * (y1 * (x1 * (t813 + py1 * t807 + t805 + t725 + t803)
			    + t799 + t568 + t731 + t189 + t730)
		      + x1 * (px1 * (x2 * y2 * (t815 + t814) + t142 * t308)
			      + t235 + t230 + t229) + t29 * (t170 + t159 + t155)
		      + t1 * (t769 + py1 * t759 + t753 + t752 + t751))
	       + py0 * (y1 * (x1 * (px1 * t812 + t194 + t193 + t192)
			      + t295 + t186 + t185 + t184)
			+ px1 * x1 * t234 + px1 * t29 * t379
			+ t1 * (px1 * t768 + t7 + t6 + t4))
	       + y1 * (x1 * (px1 * (t773 + t810 + t771) + t278) + t277 + t275)
	       + px1 * x1 * t374 + px1 * t29 * t373
	       + t1 * (px1 * (t765 + t45 + t764) + t41))
	 + y0 * (x0 * (px0 * (x1 * (t813 + py1 * t812 + t773 + t810 + t771)
			      + t495 + t29 * (t809 + t149 + t346 + t774)
			      + y1
			      * (x1
				 * (px1 * (x2 * t386 + t686 * y2)
				    + t598 + t503 + t800)
				 + t798 + py1 * t790 + t787 + t752 + t786)
			      + (t808 + t389 + t95 + t747) * t1)
		       + py0 * (x1 * (px1 * t807 + t219 + t218 + t217)
				+ px1 * t490 + t29 * t380
				+ y1 * (px1 * x1 * t597 + px1 * t797 + t7 + t6 + t4)
				+ t679 * t1)
		       + x1 * (px1 * (t805 + t725 + t803) + t195) + px1 * t802
		       + t29 * t377
		       + y1 * (px1 * x1 * t801 + px1 * (t795 + t46 + t794) + t41)
		       + t663 * t1)
		 + px0 * (x1 * (t799 + t243 + t238 + t237 + t236)
			  + t29 * (t783 + py1 * t777 + t773 + t772 + t771)
			  + y1 * (x1 * (t798 + py1 * t797 + t795 + t46 + t794)
				  + px1 * (t5 * (t481 + t609) + x2 * (t792 + t791) * y2)
				  + t546 + t670 + t669) + (t538 + t648 + t645) * t1)
		 + py0 * (x1 * (t745 + t212 + t211 + t210)
			  + t29 * (px1 * t782 + t219 + t218 + t217)
			  + y1 * (x1 * (px1 * t790 + t40 + t39 + t38) + t682)
			  + px1 * t411 * t1) + x1 * (t739 + t187)
		 + t29 * (px1 * (t727 + t780 + t724) + t195)
		 + y1 * (x1 * (px1 * (t787 + t752 + t786) + t8) + t672)
		 + px1 * t666 * t1)
	 + t161 * (px0 * (y1
			  * (t783 + x1 * (t368 + t157 + t364 + t363) + py1 * t782
			     + t727 + t780 + t724)
			  + x1 * (t383 + t372 + t371) + t500
			  + t1 * (t762 + py1 * t756 + t125 + t272 + t749))
		   + py0 * (y1
			    * (x1 * (px1 * t779 + t352 + t351 + t350)
			       + px1 * t777 + t194 + t193 + t192)
			    + px1 * x1 * t169 + px1 * t209
			    + t1 * (px1 * t761 + t84 + t83 + t82))
		   + y1 * (x1 * (px1 * t775 + t344) + px1 * (t773 + t772 + t771)
			   + t278) + px1 * x1 * t160
		   + px1 * t152 + t1 * (px1 * (t144 + t245 + t760) + t50))
	 + px0 * t770
	 + t14 * (px0 * (x1 * (t769 + py1 * t768 + t765 + t45 + t764)
			 + t29 * (t762 + py1 * t761 + t144 + t245 + t760) + t406
			 + (t412 + x1 * (t711 + t431 + t27 + t661) + t665 + t664)
			 * y1)
		  + py0 * (x1 * (px1 * t759 + t40 + t39 + t38)
			   + t732 + t29 * (px1 * t756 + t49 + t48 + t47)
			   + (px1 * t537 + x1 * (px1 * t755 + t3 + t23 + t22)) * y1)
		  + x1 * (px1 * (t753 + t752 + t751) + t8) + t655
		  + t29 * (px1 * (t125 + t272 + t749) + t269)
		  + (x1 * (px1 * t748 + t262) + px1 * t653) * y1));

    trans->m[1][0] = (x0 * (py0 * (x1 * (t516 + py1 * (y2 * (t631 + t806) + t142 * t933) + t372
					 + t371)
				   + y1 * (px1 * t929 + x1 * (t514 + t918 + t157 + t364 + t363) + t908
					   + t893 + t725 + t892) + t500 + t151
				   + t1 * (px1 * t855 + t845 + t325 + t125 + t835) + t146)
			    + px0 * (y1 * (x1 * t932 + py1 * t904 + t872 + t871 + t870)
				     + py1 * x1 * t515 + py1 * t638
				     + t1 * (py1 * t844 + t833 + t832 + t831))
			    + y1 * (x1 * t931 + t863 + py1 * (t247 + t810 + t244)) + py1 * t374
			    + py1 * x1 * t373 + t1 * (t888 + py1 * (t54 + t282 + t841)))
		      + y0 * (px0 * (x1 * (py1 * t929 + t862 + t861 + t860)
				     + t927 + py1 * t29 * t182 + t858
				     + y1 * (py1 * t896 + x1 * (py1 * t914 + t833 + t832 + t831)) + t857
				     + t856 + (t926 + t817 + t823 + t822) * t1)
			      + x0 * (px0 * (x1 * t925 + py1 * t878 + t862
					     + y1
					     * (py1 * t902 + py1 * x1 * t916 + t853 + t852
						+ t851) + t861 + t860
					     + py1 * t693 * t1)
				      + py0 * (x1 * (t921 + t918 + t156 + t346 + t345)
					       + t883 + px1 * t875 + t247
					       + y1 * (x1 * (t917 + py1 * (t328 + t88 + (t915 + t718) * y2)
							     + t153 + t55 + t339)
						       + t899 + px1 * t914 + t322 + t143 + t245 + t887) + t772
					       + t244 + (t647 + t20 + t19 + t18) * t1) + x1 * t913 + t891
				      + py1 * (t202 + t780 + t196)
				      + y1 * (py1 * (t200 + t125 + t898) + t834 + py1 * x1 * t340)
				      + py1 * t338 * t1)
			      + py0 * (x1 * (t908 + px1 * t904 + t247 + t810 + t244)
				       + t903 + py1 * t29 * t179 + t722
				       + y1 * (x1 * (px1 * t902 + t899 + t200 + t125 + t898)
					       + t897 + py1 * (x2 * t894 + x2 * (t86 + t628) * y2) + t267
					       + t266) + t237 + t721 + (t593 + t389 + t95 + t747) * t1)
			      + x1 * (py1 * (t893 + t725 + t892) + t891) + t890 + t889 + py1 * t29 * t274
			      + y1 * (x1 * (t888 + py1 * (t322 + t143 + t245 + t887)) + py1 * t268)
			      + (t885 + t884) * t1)
		      + py0 * (y1 * (x1 * (t883 + px1 * t878 + t202 + t780 + t196)
				     + t641 + t731 + t189 + t730)
			       + x1 * (t639 + t230 + t229) + py1 * t29 * t169
			       + t1 * (t840 + px1 * t850 + t13 + t752 + t825))
		      + px0 * (y1 * (x1 * (py1 * t875 + t872 + t871 + t870)
				     + py1 * t869 + t866 + t865 + t864)
			       + py1 * x1 * t499 + py1 * t29 * t174
			       + t1 * (py1 * t838 + t829 + t828 + t827))
		      + y1 * (x1 * (t863 + py1 * (t247 + t772 + t244)) + t859 + py1 * t723)
		      + py0 * t161 * t183 + py1 * t29 * t160 + py1 * x1 * t152
		      + t14 * (px0 * (x1 * (py1 * t855 + t853 + t852 + t851)
				      + py1 * t850 + py1 * t29 * t109 + t820 + t819 + t818
				      + (py1 * t592 + t826 + py1 * x1 * t646 + t847 + t846) * y1)
			       + py0 * (x1 * (t845 + px1 * t844 + t54 + t282 + t841)
					+ t840 + px1 * t838 + py1 * t29 * t76 + t46 + t659 + t816
					+ (t421 + t431 + x1 * (t836 + t26 + t94 + t96) + t27 + t661) * y1)
			       + x1 * (py1 * (t325 + t125 + t835) + t834) + t830
			       + py1 * (t13 + t752 + t825) + py1 * t29 * t36
			       + (t824 + py1 * t748 + py1 * x1 * t21) * y1)
		      + t1 * (t821 + py1 * (t46 + t659 + t816)))
	;

    trans->m[1][1] = (t161 * (px0 * (x1 * (py1 * t603 + t911 + t910 + t909)
				     + py1 * t980 + t872
				     + y1 * (py1 * t944 + py1 * x1 * t109 + t833 + t832 + t831) + t871
				     + t870 + py1 * t646 * t1)
			      + py0 * (x1 * (t514 + t157 + t364 + t363)
				       + px1 * t974 + t202
				       + y1 * (x1 * (t917 + t153 + t55 + t339)
					       + t943 + px1 * t955 + t143 + t124 + t938) + t978 + t725
				       + t196 + py1 * t66 * t1) + x1 * (t930 + py1 * t775) + t863
			      + py1 * (t773 + t283 + t771)
			      + y1 * (py1 * (t58 + t144 + t942) + t888 + py1 * x1 * t36)
			      + py1 * t21 * t1)
		      + x0 * (py0 * (t29 * (t604 + t149 + t346 + t774)
				     + t984 + t190
				     + y1 * (x1 * (px1 * t972 + t970 + t313 + t144 + t960)
					     + px1 * t958 + t950 + t787 + t12 + t939)
				     + x1 * (t607 + t983 + py1 * y2 * (x2 * (t328 + t88) + t293 + t327))
				     + t189 + t188 + py1 * t650 * t1)
			      + px0 * (t29 * t925 + t982 + t866
				       + y1
				       * (x1 * (py1 * t969 + t853 + t852 + t851)
					  + py1 * t947 + t829 + t828 + t827)
				       + py1 * x1 * y2 * t334 + t865 + t864 + py1 * t651 * t1)
			      + t29 * t913 + t859 + t981
			      + y1 * (x1 * (t834 + py1 * (t633 + t54 + t272 + t968))
				      + py1 * (t946 + t46 + t945) + t821) + py1 * x1 * y2 * t606
			      + py1 * t737 * t1)
		      + py0 * (x1 * (t573 + t238 + t237 + t236)
			       + t29 * (px1 * t980 + t773 + t978 + t283 + t771)
			       + y1 * (x1 * (t965 + px1 * t971 + t765 + t659 + t959) + t550 + t670 + t669)
			       + py1 * t537 * t1)
		      + px0 * (x1 * (py1 * t977 + t858 + t857 + t856)
			       + t29 * (py1 * t974 + t862 + t861 + t860)
			       + y1 * (x1 * (py1 * t966 + t820 + t819 + t818) + t973) + py1 * t539 * t1)
		      + x1 * (t890 + py1 * t191) + t29 * (t891 + py1 * (t202 + t725 + t196))
		      + y0 * (x0 * (px0 * (x1 * (py1 * t972 + t833 + t832 + t831)
					   + py1 * t971 + py1 * t29 * t916 + t829 + t828 + t827
					   + (t926 + t817 + py1 * x1 * t693 + t823 + t822) * y1)
				    + py0 * (x1 * (t970 + px1 * t969 + t633 + t54 + t272 + t968)
					     + px1 * t966 + t965 + t29 * (t163 + t35 + t34 + t33) + t753 + t12
					     + t935
					     + (t962 + t941 + t43
						+ x1
						* (t836 + py1 * (t86 + t628 + (t961 + t291) * x2)
						   + t26 + t94 + t96) + t95 + t93)
					     * y1) + x1 * (py1 * (t313 + t144 + t960) + t888) + t821
				    + py1 * (t765 + t659 + t959) + py1 * t29 * t340
				    + (t885 + t884 + py1 * x1 * t338) * y1)
			      + px0 * (x1 * (py1 * t958 + t820 + t819 + t818)
				       + t956 + t29 * (py1 * t955 + t853 + t852 + t851)
				       + (py1 * t415 + x1 * t953) * y1)
			      + py0 * (x1 * (t950 + px1 * t947 + t946 + t46 + t945)
				       + t29 * (px1 * t944 + t943 + t58 + t144 + t942) + t406 + t392 + t391
				       + (x1 * (t421 + t941 + t431 + t27 + t661)
					  + t416 + py1 * (x2 * (t554 + t796) + t5 * t940) + t665 + t664)
				       * y1) + x1 * (py1 * (t787 + t12 + t939) + t830)
			      + t29 * (t834 + py1 * (t143 + t124 + t938)) + t937
			      + (x1 * t936 + py1 * t666) * y1)
		      + y1 * (x1 * (py1 * (t753 + t12 + t935) + t830) + t934) + py1 * t653 * t1
		      + py0 * t652 * t14)
	;

    trans->m[1][2] = (y0 * (x0 * (px0 * (x1 * (py1 * t1016 + t872 + t871 + t870)
					 + py1 * t494 + t29 * t932
					 + y1
					 * (py1 * t1025 + py1 * x1 * t599 + t820 + t819
					    + t818) + t953 * t1)
				  + py0 * (x1 * (t1015 + px1 * t1009 + t727 + t326 + t724)
					   + t29 * (t1028 + t148 + t364 + t744) + t491
					   + y1
					   * (x1
					      * (t600 + py1 * (x2 * t933 + t940 * y2) + t503
						 + t800)
					      + px1 * t1020 + t1026 + t676 + t45 + t1017)
					   + (t1027 + t10 + t27 + t25) * t1)
				  + x1 * (py1 * (t1014 + t283 + t1012) + t863) + t29 * t931
				  + py1 * t802
				  + y1 * (py1 * x1 * t801 + py1 * (t1024 + t13 + t1023) + t830)
				  + t936 * t1)
			    + py0 * (t29 * (t1007 + px1 * t1002 + t727 + t201 + t724)
				     + x1 * (t1011 + t641 + t731 + t189 + t730)
				     + y1 * (x1 * (t1026 + px1 * t1025 + t1024 + t13 + t1023)
					     + t550
					     + py1
					     * (t5 * (t293 + t327) + x2 * (t1021 + t697) * y2)
					     + t670 + t669) + (t540 + t648 + t645) * t1)
			    + px0 * (x1 * (t982 + t866 + t865 + t864)
				     + t29 * (py1 * t1006 + t872 + t871 + t870)
				     + y1 * (x1 * (py1 * t1020 + t829 + t828 + t827) + t956)
				     + py1 * t415 * t1) + x1 * (t859 + t981)
			    + t29 * (t863 + py1 * (t773 + t246 + t771))
			    + y1 * (x1 * (py1 * (t676 + t45 + t1017) + t821) + t937)
			    + py1 * t666 * t1)
		      + x0 * (py0 * (y1 * (x1 * (px1 * t1016 + t1015 + t1014 + t283 + t1012)
					   + t1011 + t573 + t238 + t237 + t236)
				     + x1 * (t639
					     + py1 * (x2 * y2 * (t1010 + t300) + t142 * t894)
					     + t230 + t229) + t29 * (t175 + t159 + t155)
				     + t1 * (px1 * t1000 + t994 + t660 + t45 + t986))
			      + px0 * (y1 * (x1 * (py1 * t1009 + t862 + t861 + t860)
					     + t927 + t858 + t857 + t856)
				       + py1 * t29 * t515 + py1 * x1 * t638
				       + t1 * (py1 * t993 + t820 + t819 + t818))
			      + y1 * (x1 * (t891 + py1 * (t727 + t326 + t724)) + t890 + t889)
			      + py1 * x1 * t374 + py1 * t29 * t373
			      + t1 * (t830 + py1 * (t705 + t752 + t990)))
		      + t161 * (py0 * (x1 * (t516 + t372 + t371)
				       + y1
				       * (x1 * (t921 + t156 + t346 + t345)
					  + t1007 + px1 * t1006 + t773 + t246 + t771) + t381
				       + t1 * (t989 + px1 * t997 + t54 + t245 + t985))
				+ px0 * (y1
					 * (x1 * (py1 * t1004 + t924 + t923 + t922)
					    + py1 * t1002 + t862 + t861 + t860)
					 + py1 * t499 + py1 * x1 * t174
					 + t1 * (py1 * t988 + t853 + t852 + t851))
				+ y1 * (x1 * (t912 + py1 * t746) + t891
					+ py1 * (t727 + t201 + t724))
				+ py1 * x1 * t160 + py1 * t152
				+ t1 * (t834 + py1 * (t143 + t272 + t987))) + py0 * t770
		      + t14 * (px0 * (x1 * (py1 * t1000 + t829 + t828 + t827)
				      + t973 + t29 * (py1 * t997 + t833 + t832 + t831)
				      + (py1 * t539 + x1 * (py1 * t996 + t817 + t823 + t822))
				      * y1)
			       + py0 * (x1 * (t994 + px1 * t993 + t705 + t752 + t990)
					+ t29 * (t989 + px1 * t988 + t143 + t272 + t987) + t398
					+ (t416 + x1 * (t962 + t43 + t95 + t93) + t665 + t664)
					* y1) + x1 * (py1 * (t660 + t45 + t986) + t821)
			       + t29 * (t888 + py1 * (t54 + t245 + t985)) + t934
			       + (x1 * (t885 + py1 * t28) + py1 * t653) * y1));

    trans->m[2][0] = (x0 * (px0 * (y1 * (x1 * t617 + t586 + t877 + t1008 + t876)
				   + x1 * t515 + t637 + t1 * (t475 + t136 + t620 + t1036) + t636)
			    + py0 * (y1 * (t579 + x1 * t627 + t251 + t811 + t248)
				     + x1 * t379 + t233 + t1 * (t455 + t87 + t298 + t1035) + t232)
			    + x1 * (t516 + t383 + y2 * (t804 + t1013) + t142 * t595)
			    + y1 * (px1 * (t1047 + t979 + t1046)
				    + x1 * (t921 + t368 + t157 + t156 + t1048)
				    + py1 * (t1044 + t728 + t1043) + t505 + t607 + t502) + t500 + t381
			    + t1 * (px1 * (t611 + t135 + t1038) + py1 * (t330 + t128 + t1037) + t423
				    + t125 + t54 + t1034))
		      + y0 * (x0 * (py0 * (x1 * t1050 + t533 + t226
					   + y1 * (t529 + t224 + x1 * t349 + t128 + t1040)
					   + t781 + t220 + t99 * t1)
				    + px0 * (t528 + x1 * t1049 + t874
					     + y1 * (t517 + x1 * t916 + t520 + t135 + t1041) + t1005
					     + t873 + t693 * t1)
				    + x1 * (t514 + t382 + t157 + t156 + t1048)
				    + px1 * (t877 + t1001 + t876) + py1 * (t251 + t776 + t248) + t608
				    + y1 * (x1 * (t917 + t358 + t56 + t323 + (t716 + t967) * y2)
					    + px1 * (t118 + t609 + t471 + t1045)
					    + py1 * (t204 + t327 + t249 + t1042) + t503 + t144 + t143
					    + t1039) + t504 + t605 + (t647 + t357) * t1)
			      + px0 * (x1 * (t528 + t1047 + t979 + t1046)
				       + t643 + t29 * t182 + t571
				       + y1 * (x1 * (t475 + t118 + t609 + t471 + t1045) + t701 + t895)
				       + t570 + t569 + (t468 + t441 + t466 + t951) * t1)
			      + py0 * (x1 * (t533 + t1044 + t728 + t1043)
				       + t642 + t29 * t179 + t566
				       + y1 * (x1 * (t455 + t204 + t327 + t249 + t1042) + t289 + t288)
				       + t214 + t565 + (t436 + t450 + t69 + t677) * t1)
			      + x1 * (px1 * (t877 + t1008 + t876) + py1 * (t251 + t811 + t248) + t505
				      + t607 + t502) + t984 + t743
			      + t29 * t274
			      + y1 * (x1 * (px1 * (t520 + t135 + t1041) + py1 * (t224 + t128 + t1040)
					    + t503 + t144 + t143 + t1039)
				      + t897 + t303 + x2 * t634 + x2 * (t690 + t52) * y2)
			      + (t1027 + t808) * t1)
		      + py0 * (y1 * (x1 * (t579 + t251 + t776 + t248) + t581 + t735 + t240 + t734)
			       + t29 * t169 + x1 * t209 + t1 * (t461 + t80 + t683 + t1032))
		      + px0 * (y1 * (x1 * (t586 + t877 + t1001 + t876) + t591 + t976 + t588 + t975)
			       + x1 * t499 + t29 * t174 + t1 * (t484 + t849 + t957 + t1033))
		      + y1 * (x1 * (px1 * (t874 + t1005 + t873) + py1 * (t226 + t781 + t220) + t608
				    + t504 + t605)
			      + t573 + t568) + t161 * t183 + x1 * (t639 + t235) + t29 * t160
		      + t14 * (px0 * (x1 * (t517 + t611 + t135 + t1038)
				      + t615 + t29 * t109 + t560 + t443 + t1031
				      + (t399 + t613 + x1 * t646 + t418 + t995) * y1)
			       + py0 * (x1 * (t529 + t330 + t128 + t1037)
					+ t625 + t29 * t76 + t112 + t438 + t1030
					+ (t623 + t393 + t102 + x1 * t66 + t754) * y1)
			       + x1 * (px1 * (t136 + t620 + t1036) + py1 * (t87 + t298 + t1035) + t423
				       + t125 + t54 + t1034)
			       + px1 * (t849 + t957 + t1033) + py1 * (t80 + t683 + t1032) + t434
			       + t29 * t36 + t1029 + (t962 + t711 + x1 * (t836 + t100)) * y1)
		      + t1 * (px1 * (t560 + t443 + t1031) + py1 * (t112 + t438 + t1030) + t434
			      + t1029))
	;

    trans->m[2][1] = (t161 * (px0 * (x1 * (t616 + t180 + t512 + t1003)
				     + t586 + t510 + y1 * (t475 + t854 + x1 * t109 + t117 + t1062)
				     + t621 + t508 + t646 * t1)
			      + py0 * (t579 + x1 * (t177 + t626 + t354 + t778) + t507
				       + y1 * (t455 + x1 * t76 + t91 + t203 + t1061) + t299
				       + t506 + t66 * t1) + x1 * (t921 + t368)
			      + px1 * (t874 + t979 + t873) + py1 * (t226 + t728 + t220)
			      + y1 * (x1 * (t917 + t358) + px1 * (t118 + t843 + t1063)
				      + py1 * (t204 + t127 + t1065) + t423 + t144
				      + t143 + t1060) + t504 + t21 * t1)
		      + x0 * (py0 * (t29 * t1050 + t581 + t241
				     + y1
				     * (x1 * (t529 + t87 + t577 + t292 + t1070)
					+ t461 + t1057 + t80 + t1056) + x1 * y2 * t741 + t240
				     + t239 + t650 * t1)
			      + px0 * (t591 + t29 * t1049 + t589
				       + y1 * (x1 * (t517 + t136 + t306 + t428 + t1071)
					       + t484 + t1059 + t849 + t1058) + x1 * y2 * t334 + t588
				       + t587 + t651 * t1) + t29 * (t1028 + t809) + t903 + t321
			      + y1 * (x1 * (px1 * (t901 + t117 + t1072) + py1 * (t317 + t203 + t1073)
					    + t503 + t125 + t54 + t1069)
				      + px1 * (t1019 + t837 + t1064) + py1 * (t789 + t111 + t1066) + t267
				      + t433 + t1055)
			      + x1 * (y2 * (x2 * (t56 + t323) + t322 + t273) + t983 + t742) + t737 * t1)
		      + py0 * (x1 * (t642 + t215 + t214 + t213) + t29 * (t533 + t226 + t728 + t220)
			       + y1
			       * (x1 * (t625 + t758 + t111 + t1067)
				  + t396 + t395) + t537 * t1)
		      + px0 * (x1 * (t643 + t868 + t570 + t867) + t29 * (t528 + t874 + t979 + t873)
			       + y1
			       * (x1 * (t615 + t999 + t837 + t1068)
				  + t404 + t402) + t539 * t1)
		      + x1 * (t641 + t243)
		      + t29 * (px1 * (t510 + t621 + t508) + py1 * (t507 + t299 + t506) + t504)
		      + y0 * (x0 * (py0 * (x1 * (t455 + t317 + t203 + t1073)
					   + t461 + t29 * t349 + t767 + t683 + t1052
					   + (t436 + t450 + x1 * t99 + t69 + t677) * y1)
				    + px0 * (x1 * (t475 + t901 + t117 + t1072)
					     + t484 + t29 * t916 + t992 + t957 + t1053
					     + (t468 + t441 + t466 + x1 * t693 + t951) * y1)
				    + x1 * (px1 * (t136 + t306 + t428 + t1071)
					    + py1 * (t87 + t577 + t292 + t1070) + t503 + t125 + t54 + t1069)
				    + px1 * (t999 + t837 + t1068) + py1 * (t758 + t111 + t1067)
				    + t29 * (t163 + t162) + t434 + t553 + t1051
				    + (t421 + t104 + t431 + t43
				       + x1 * (t836 + t100 + t690 + t52 + (t270 + t886) * x2)
				       + t1054)
				    * y1)
			      + py0 * (x1 * (t625 + t789 + t111 + t1066)
				       + t29 * (t529 + t204 + t127 + t1065) + t544 + t543
				       + (x1 * t624 + t410 + t409) * y1)
			      + px0 * (x1 * (t615 + t1019 + t837 + t1064)
				       + t29 * (t517 + t118 + t843 + t1063) + t548 + t547
				       + (x1 * t614 + t414 + t413) * y1)
			      + t29 * (px1 * (t854 + t117 + t1062) + py1 * (t91 + t203 + t1061) + t423
				       + t144 + t143 + t1060)
			      + x1 * (px1 * (t1059 + t849 + t1058) + py1 * (t1057 + t80 + t1056) + t267
				      + t433 + t1055) + t406 + t398
			      + (t416 + x1 * (t962 + t711 + t431 + t43 + t1054) + t412
				 + x2 * (t793 + t1022) + t5 * t594)
			      * y1)
		      + y1 * (x1 * (px1 * (t992 + t957 + t1053) + py1 * (t767 + t683 + t1052) + t434
				    + t553 + t1051)
			      + t550 + t546) + t653 * t1 + t652 * t14)
	;
    trans->m[2][2] = t644;
}

static void
print_trans (const char *header, struct pixman_f_transform *trans)
{
    int i, j;
    double max;

    max = 0;

    printf ("%s\n", header);

    for (i = 0; i < 3; ++i)
    {
	for (j = 0; j < 3; ++j)
	{
	    double a = fabs (trans->m[i][j]);

	    if (a > max)
		max = a;
	}
    }

    if (max == 0.0)
	max = 1.0;

    for (i = 0; i < 3; ++i)
    {
	printf ("{ ");
	for (j = 0; j < 3; ++j)
	{
	    printf ("D2F (%.5f)%s", 16384 * (trans->m[i][j] / max), j == 2 ? "" : ", ");
	}

	printf ("},\n");
    }
}

int
main ()
{
    struct pixman_f_transform t;

#if 0
    quad_to_quad (75, 200,
		  325, 200,
		  450, 335,
		  -50, 335,

		  0, 0,
		  400, 0,
		  400, 400,
		  0, 400,

		  &t);
#endif
    quad_to_quad (
	1, 0,
	1, 2,
	2, 2,
	2, 0,

	1, 0,
	1, 112,
	2, 2,
	2, 0,

	&t);

    print_trans ("0->0", &t);

    return 0;
}
