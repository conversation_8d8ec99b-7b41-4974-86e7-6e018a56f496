<?xml version="1.0" encoding="UTF-8"?>
<interface>
  <requires lib="gtk+" version="2.12"/>
  <object class="GtkWindow" id="main">
    <property name="can_focus">False</property>
    <child>
      <placeholder/>
    </child>
    <child>
      <object class="GtkHBox" id="u">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="spacing">12</property>
        <child>
          <object class="GtkScrolledWindow" id="scrolledwindow1">
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="shadow_type">in</property>
            <child>
              <object class="GtkViewport" id="viewport1">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <child>
                  <object class="GtkDrawingArea" id="drawing_area">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
          <packing>
            <property name="expand">True</property>
            <property name="fill">True</property>
            <property name="position">0</property>
          </packing>
        </child>
        <child>
          <object class="GtkVBox" id="box1">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="spacing">12</property>
            <child>
              <object class="GtkVBox" id="box6">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <child>
                  <object class="GtkTable" id="grid1">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="n_rows">2</property>
                    <property name="n_columns">2</property>
                    <property name="column_spacing">8</property>
                    <property name="row_spacing">6</property>
                    <child>
                      <object class="GtkLabel" id="label4">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                        <property name="label" translatable="yes">&lt;b&gt;Target format:&lt;/b&gt;</property>
                        <property name="use_markup">True</property>
                        <property name="xalign">1</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkLabel" id="label5">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                        <property name="label" translatable="yes">&lt;b&gt;Dithering:&lt;/b&gt;</property>
                        <property name="use_markup">True</property>
                        <property name="xalign">1</property>
                      </object>
                      <packing>
                        <property name="top_attach">1</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkComboBox" id="target_format_combo_box">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                      </object>
                      <packing>
                        <property name="left_attach">1</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkComboBox" id="dithering_combo_box">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                      </object>
                      <packing>
                        <property name="left_attach">1</property>
                        <property name="top_attach">1</property>
                      </packing>
                    </child>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="fill">True</property>
                    <property name="padding">6</property>
                    <property name="position">1</property>
                  </packing>
                </child>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">0</property>
              </packing>
            </child>
          </object>
          <packing>
            <property name="expand">False</property>
            <property name="fill">True</property>
            <property name="position">1</property>
          </packing>
        </child>
      </object>
    </child>
  </object>
  <object class="GtkAdjustment" id="rotate_adjustment">
    <property name="lower">-180</property>
    <property name="upper">190</property>
    <property name="step_increment">1</property>
    <property name="page_increment">10</property>
    <property name="page_size">10</property>
  </object>
  <object class="GtkAdjustment" id="scale_x_adjustment">
    <property name="lower">-32</property>
    <property name="upper">42</property>
    <property name="step_increment">1</property>
    <property name="page_increment">10</property>
    <property name="page_size">10</property>
  </object>
  <object class="GtkAdjustment" id="scale_y_adjustment">
    <property name="lower">-32</property>
    <property name="upper">42</property>
    <property name="step_increment">1</property>
    <property name="page_increment">10</property>
    <property name="page_size">10</property>
  </object>
  <object class="GtkAdjustment" id="subsample_adjustment">
    <property name="upper">12</property>
    <property name="value">4</property>
    <property name="step_increment">1</property>
    <property name="page_increment">1</property>
  </object>
</interface>
