<?xml version="1.0" encoding="UTF-8"?>
<interface>
  <!-- interface-requires gtk+ 2.12 -->
  <!-- interface-naming-policy toplevel-contextual -->
  <object class="GtkAdjustment" id="rotate_adjustment">
    <property name="lower">-180</property>
    <property name="upper">190</property>
    <property name="step_increment">1</property>
    <property name="page_increment">10</property>
    <property name="page_size">10</property>
  </object>
  <object class="GtkAdjustment" id="scale_y_adjustment">
    <property name="lower">-32</property>
    <property name="upper">42</property>
    <property name="step_increment">1</property>
    <property name="page_increment">10</property>
    <property name="page_size">10</property>
  </object>
  <object class="GtkAdjustment" id="scale_x_adjustment">
    <property name="lower">-32</property>
    <property name="upper">42</property>
    <property name="step_increment">1</property>
    <property name="page_increment">10</property>
    <property name="page_size">10</property>
  </object>
  <object class="GtkAdjustment" id="subsample_adjustment">
    <property name="lower">0</property>
    <property name="upper">12</property>
    <property name="step_increment">1</property>
    <property name="page_increment">1</property>
    <property name="page_size">0</property>
    <property name="value">4</property>
  </object>
  <object class="GtkWindow" id="main">
    <child>
      <object class="GtkHBox" id="u">
        <property name="visible">True</property>
        <property name="spacing">12</property>
        <child>
          <object class="GtkScrolledWindow" id="scrolledwindow1">
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="shadow_type">in</property>
            <child>
              <object class="GtkViewport" id="viewport1">
                <property name="visible">True</property>
                <child>
                  <object class="GtkDrawingArea" id="drawing_area">
                    <property name="visible">True</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
          <packing>
            <property name="position">0</property>
          </packing>
        </child>
        <child>
          <object class="GtkVBox" id="box1">
            <property name="visible">True</property>
	    <property name="spacing">12</property>
            <child>
              <object class="GtkHBox" id="box2">
                <property name="visible">True</property>
                <property name="homogeneous">True</property>
                <child>
                  <object class="GtkVBox" id="box3">
                    <property name="visible">True</property>
                    <property name="spacing">6</property>
                    <child>
                      <object class="GtkLabel" id="label1">
                        <property name="visible">True</property>
                        <property name="label" translatable="yes">&lt;b&gt;Scale X&lt;/b&gt;</property>
                        <property name="use_markup">True</property>
                      </object>
                      <packing>
                        <property name="expand">False</property>
                        <property name="position">0</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkVScale" id="scale_x_scale">
                        <property name="visible">True</property>
                        <property name="can_focus">True</property>
                        <property name="adjustment">scale_x_adjustment</property>
                        <property name="fill_level">32</property>
                        <property name="value_pos">right</property>
                      </object>
                      <packing>
                        <property name="position">1</property>
                      </packing>
                    </child>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="position">0</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkVBox" id="box4">
                    <property name="visible">True</property>
                    <property name="spacing">6</property>
                    <child>
                      <object class="GtkLabel" id="label2">
                        <property name="visible">True</property>
                        <property name="label" translatable="yes">&lt;b&gt;Scale Y&lt;/b&gt;</property>
                        <property name="use_markup">True</property>
                      </object>
                      <packing>
                        <property name="expand">False</property>
                        <property name="position">0</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkVScale" id="scale_y_scale">
                        <property name="visible">True</property>
                        <property name="can_focus">True</property>
                        <property name="adjustment">scale_y_adjustment</property>
                        <property name="fill_level">32</property>
                        <property name="value_pos">right</property>
                      </object>
                      <packing>
                        <property name="position">1</property>
                      </packing>
                    </child>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="position">1</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkVBox" id="box5">
                    <property name="visible">True</property>
                    <property name="spacing">6</property>
                    <child>
                      <object class="GtkLabel" id="label3">
                        <property name="visible">True</property>
                        <property name="label" translatable="yes">&lt;b&gt;Rotate&lt;/b&gt;</property>
                        <property name="use_markup">True</property>
                      </object>
                      <packing>
                        <property name="expand">False</property>
                        <property name="position">0</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkVScale" id="rotate_scale">
                        <property name="visible">True</property>
                        <property name="can_focus">True</property>
                        <property name="adjustment">rotate_adjustment</property>
                        <property name="fill_level">180</property>
                        <property name="value_pos">right</property>
                      </object>
                      <packing>
                        <property name="position">1</property>
                      </packing>
                    </child>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="position">2</property>
                  </packing>
                </child>
              </object>
              <packing>
                <property name="padding">6</property>
                <property name="position">0</property>
              </packing>
            </child>
            <child>
              <object class="GtkVBox" id="box6">
                <property name="visible">True</property>
		<child>
		  <object class="GtkCheckButton"
			  id="lock_checkbutton">
		    <property name="label" translatable="yes">Lock X and Y Dimensions</property>
		    <property name="xalign">0.0</property>
		    <property name="active">True</property>
		  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="fill">False</property>
                    <property name="padding">6</property>
                    <property name="position">1</property>
                  </packing>
		</child>
                <child>
                  <object class="GtkTable" id="grid1">
                    <property name="visible">True</property>
                    <property name="column_spacing">8</property>
                    <property name="row_spacing">6</property>
                    <child>
                      <object class="GtkLabel" id="label4">
                        <property name="visible">True</property>
                        <property name="xalign">1</property>
                        <property name="label" translatable="yes">&lt;b&gt;Reconstruct X:&lt;/b&gt;</property>
                        <property name="use_markup">True</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkLabel" id="label5">
                        <property name="visible">True</property>
                        <property name="xalign">1</property>
                        <property name="label" translatable="yes">&lt;b&gt;Reconstruct Y:&lt;/b&gt;</property>
                        <property name="use_markup">True</property>
                      </object>
                      <packing>
                        <property name="top_attach">1</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkLabel" id="label6">
                        <property name="visible">True</property>
                        <property name="xalign">1</property>
                        <property name="label" translatable="yes">&lt;b&gt;Sample X:&lt;/b&gt;</property>
                        <property name="use_markup">True</property>
                      </object>
                      <packing>
                        <property name="top_attach">2</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkLabel" id="label7">
                        <property name="visible">True</property>
                        <property name="xalign">1</property>
                        <property name="label" translatable="yes">&lt;b&gt;Sample Y:&lt;/b&gt;</property>
                        <property name="use_markup">True</property>
                      </object>
                      <packing>
                        <property name="top_attach">3</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkLabel" id="label8">
                        <property name="visible">True</property>
                        <property name="xalign">1</property>
                        <property name="label" translatable="yes">&lt;b&gt;Repeat:&lt;/b&gt;</property>
                        <property name="use_markup">True</property>
                      </object>
                      <packing>
                        <property name="top_attach">4</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkLabel" id="label9">
                        <property name="visible">True</property>
                        <property name="xalign">1</property>
                        <property name="label" translatable="yes">&lt;b&gt;Subsample:&lt;/b&gt;</property>
                        <property name="use_markup">True</property>
                      </object>
                      <packing>
                        <property name="top_attach">5</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkComboBox" id="reconstruct_x_combo_box">
                        <property name="visible">True</property>
                      </object>
                      <packing>
                        <property name="left_attach">1</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkComboBox" id="reconstruct_y_combo_box">
                        <property name="visible">True</property>
                      </object>
                      <packing>
                        <property name="left_attach">1</property>
                        <property name="top_attach">1</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkComboBox" id="sample_x_combo_box">
                        <property name="visible">True</property>
                      </object>
                      <packing>
                        <property name="left_attach">1</property>
                        <property name="top_attach">2</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkComboBox" id="sample_y_combo_box">
                        <property name="visible">True</property>
                      </object>
                      <packing>
                        <property name="left_attach">1</property>
                        <property name="top_attach">3</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkComboBox" id="repeat_combo_box">
                        <property name="visible">True</property>
                      </object>
                      <packing>
                        <property name="left_attach">1</property>
                        <property name="top_attach">4</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkSpinButton" id="subsample_spin_button">
                        <property name="visible">True</property>
			<property name="adjustment">subsample_adjustment</property>
			<property name="value">4</property>
                      </object>
                      <packing>
                        <property name="left_attach">1</property>
                        <property name="top_attach">5</property>
                      </packing>
                    </child>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="padding">6</property>
                    <property name="position">1</property>
                  </packing>
                </child>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="position">0</property>
              </packing>
            </child>
          </object>
          <packing>
            <property name="expand">False</property>
            <property name="position">1</property>
          </packing>
        </child>
      </object>
    </child>
  </object>
</interface>
