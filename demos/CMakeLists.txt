# Copyright © 2018 Intel Corporation
# Copyright © 2024 Pixman Contributors

# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:

# The above copyright notice and this permission notice shall be included in
# all copies or substantial portions of the Software.

# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
# SOFTWARE.

# Extra demo compile flags for MSVC
set(EXTRA_DEMO_CFLAGS)
if(CMAKE_C_COMPILER_ID STREQUAL "MSVC")
    set(EXTRA_DEMO_CFLAGS "-D_USE_MATH_DEFINES")
endif()

# Demo programs
set(DEMOS
    gradient-test
    alpha-test
    composite-test
    clip-test
    trap-test
    screen-test
    convolution-test
    radial-test
    linear-gradient
    conical-test
    tri-test
    checkerboard
    srgb-test
    srgb-trap-test
    scale
    dither
)

# Only build demos if GTK is available
if(HAVE_GTK)
    # Create demo utility library
    add_library(demo STATIC
        gtk-utils.c
    )
    
    # Set include directories for demo library
    target_include_directories(demo
        PRIVATE
            ${CMAKE_BINARY_DIR}
            ${CMAKE_CURRENT_BINARY_DIR}
    )
    
    # Link demo library with dependencies
    target_link_libraries(demo
        PUBLIC
            pixman::pixman-1
            pixman::testutils
        PRIVATE
            ${GTK3_LIBRARIES}
            ${GLIB2_LIBRARIES}
    )
    
    # Set compile options for demo library
    target_compile_options(demo PRIVATE ${GTK3_CFLAGS_OTHER} ${GLIB2_CFLAGS_OTHER})
    target_include_directories(demo PRIVATE ${GTK3_INCLUDE_DIRS} ${GLIB2_INCLUDE_DIRS})
    
    # Link with PNG if available
    if(PNG_FOUND)
        target_link_libraries(demo PRIVATE PNG::PNG)
    endif()
    
    # Link with math library if available
    if(MATH_LIBRARY)
        target_link_libraries(demo PRIVATE ${MATH_LIBRARY})
    endif()
    
    # Link with OpenMP if available
    if(OpenMP_C_FOUND)
        target_link_libraries(demo PRIVATE OpenMP::OpenMP_C)
    endif()
    
    # Create demo executables
    foreach(demo_name ${DEMOS})
        add_executable(${demo_name} ${demo_name}.c)
        
        # Set compile options
        target_compile_options(${demo_name} PRIVATE ${EXTRA_DEMO_CFLAGS})
        
        # Set include directories
        target_include_directories(${demo_name}
            PRIVATE
                ${CMAKE_BINARY_DIR}
                ${CMAKE_CURRENT_BINARY_DIR}
                ${GTK3_INCLUDE_DIRS}
                ${GLIB2_INCLUDE_DIRS}
        )
        
        # Link with libraries
        target_link_libraries(${demo_name}
            PRIVATE
                demo
                pixman::pixman-1
                pixman::testutils
                ${GTK3_LIBRARIES}
                ${GLIB2_LIBRARIES}
        )
        
        # Set compile options
        target_compile_options(${demo_name} PRIVATE ${GTK3_CFLAGS_OTHER} ${GLIB2_CFLAGS_OTHER})
        
        # Link with OpenMP if available
        if(OpenMP_C_FOUND)
            target_link_libraries(${demo_name} PRIVATE OpenMP::OpenMP_C)
        endif()
        
        # Link with PNG if available
        if(PNG_FOUND)
            target_link_libraries(${demo_name} PRIVATE PNG::PNG)
        endif()
    endforeach()
endif()
